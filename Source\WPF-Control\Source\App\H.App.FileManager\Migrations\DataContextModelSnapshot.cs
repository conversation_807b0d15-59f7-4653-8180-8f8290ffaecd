﻿// <auto-generated />
using System;
using H.App.FileManager;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace H.App.FileManager.Migrations
{
    [DbContext(typeof(DataContext))]
    partial class DataContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "7.0.8")
                .HasAnnotation("Proxies:ChangeTracking", false)
                .HasAnnotation("Proxies:CheckEquality", false)
                .HasAnnotation("Proxies:LazyLoading", true);

            modelBuilder.Entity("H.App.FileManager.fm_dd_file", b =>
                {
                    b.Property<string>("ID")
                        .HasColumnType("TEXT")
                        .HasColumnName("id")
                        .HasColumnOrder(0);

                    b.Property<DateTime>("CDATE")
                        .HasColumnType("TEXT");

                    b.Property<string>("Discriminator")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("Extend")
                        .HasColumnType("TEXT");

                    b.Property<bool>("Favorite")
                        .HasColumnType("INTEGER");

                    b.Property<string>("FavoritePath")
                        .HasColumnType("TEXT");

                    b.Property<int>("ISENBLED")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("LastPlayTime")
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<int>("PlayCount")
                        .HasColumnType("INTEGER");

                    b.Property<int>("Score")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("SeeLater")
                        .HasColumnType("INTEGER");

                    b.Property<long>("Size")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Tags")
                        .HasColumnType("TEXT");

                    b.Property<string>("Type")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("UDATE")
                        .HasColumnType("TEXT");

                    b.Property<string>("Url")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<bool>("Watched")
                        .HasColumnType("INTEGER");

                    b.HasKey("ID");

                    b.ToTable("fm_dd_files");

                    b.HasDiscriminator<string>("Discriminator").HasValue("fm_dd_file");

                    b.UseTphMappingStrategy();
                });

            modelBuilder.Entity("H.App.FileManager.fm_dd_audio", b =>
                {
                    b.HasBaseType("H.App.FileManager.fm_dd_file");

                    b.HasDiscriminator().HasValue("fm_dd_audio");
                });

            modelBuilder.Entity("H.App.FileManager.fm_dd_image", b =>
                {
                    b.HasBaseType("H.App.FileManager.fm_dd_file");

                    b.Property<string>("Area")
                        .HasColumnType("TEXT");

                    b.Property<string>("Articulation")
                        .HasColumnType("TEXT");

                    b.Property<string>("Introduction")
                        .HasColumnType("TEXT");

                    b.Property<string>("Object")
                        .HasColumnType("TEXT");

                    b.Property<int>("PixelHeight")
                        .HasColumnType("INTEGER");

                    b.Property<int>("PixelWidth")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Year")
                        .HasColumnType("TEXT");

                    b.HasDiscriminator().HasValue("fm_dd_image");
                });

            modelBuilder.Entity("H.App.FileManager.fm_dd_video", b =>
                {
                    b.HasBaseType("H.App.FileManager.fm_dd_image");

                    b.Property<string>("Bitrate")
                        .HasColumnType("TEXT");

                    b.Property<long>("Duration")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Image")
                        .HasColumnType("TEXT");

                    b.Property<long>("LastPlayTimeStamp")
                        .HasColumnType("INTEGER");

                    b.Property<string>("PixelFormat")
                        .HasColumnType("TEXT");

                    b.Property<string>("Rate")
                        .HasColumnType("TEXT");

                    b.Property<int>("SelectedImageIndex")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Torrent")
                        .HasColumnType("TEXT");

                    b.Property<string>("VideoCode")
                        .HasColumnType("TEXT");

                    b.HasDiscriminator().HasValue("fm_dd_video");
                });

            modelBuilder.Entity("H.App.FileManager.fm_dd_video_image", b =>
                {
                    b.HasBaseType("H.App.FileManager.fm_dd_image");

                    b.Property<string>("DisplayName")
                        .HasColumnType("TEXT");

                    b.Property<long>("TimeStamp")
                        .HasColumnType("INTEGER");

                    b.Property<string>("fm_dd_videoID")
                        .HasColumnType("TEXT");

                    b.HasIndex("fm_dd_videoID");

                    b.HasDiscriminator().HasValue("fm_dd_video_image");
                });

            modelBuilder.Entity("H.App.FileManager.fm_dd_video_image", b =>
                {
                    b.HasOne("H.App.FileManager.fm_dd_video", null)
                        .WithMany("Images")
                        .HasForeignKey("fm_dd_videoID");
                });

            modelBuilder.Entity("H.App.FileManager.fm_dd_video", b =>
                {
                    b.Navigation("Images");
                });
#pragma warning restore 612, 618
        }
    }
}
