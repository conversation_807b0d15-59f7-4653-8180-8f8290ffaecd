﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:p="clr-namespace:H.Components.Modbus.Presenters">
    <DataTemplate DataType="{x:Type p:ModbusDataState}">
        <DockPanel x:Name="dp">
            <FontIconTextBlock x:Name="tb"
                               Margin="5,0" />
            <TextBlock x:Name="tb_name" />
        </DockPanel>
        <DataTemplate.Triggers>
            <DataTrigger Binding="{Binding}"
                         Value="Running">
                <Setter TargetName="tb" Property="Text" Value="&#xE8ED;" />
                <Setter TargetName="dp" Property="TextBlock.Foreground" Value="{DynamicResource {x:Static BrushKeys.Green}}" />
                <Setter TargetName="tb_name" Property="Text" Value="读取中..." />
                <!--<DataTrigger.EnterActions>
                    <BeginStoryboard x:Name="BeginStoryboard"
                                     Storyboard="{DynamicResource {x:Static StoryBoardKeys.OpacityForever}}" />
                </DataTrigger.EnterActions>
                <DataTrigger.ExitActions>
                    <StopStoryboard BeginStoryboardName="BeginStoryboard" />
                </DataTrigger.ExitActions>-->
            </DataTrigger>
            <DataTrigger Binding="{Binding}"
                         Value="Canceling">
                <Setter TargetName="tb" Property="Text" Value="&#xEA3F;" />
                <Setter TargetName="dp" Property="TextBlock.Foreground" Value="{DynamicResource {x:Static BrushKeys.Green}}" />
                <Setter TargetName="tb_name" Property="Text" Value="正在停止..." />
            </DataTrigger>
            <DataTrigger Binding="{Binding}"
                         Value="Stopped">
                <Setter TargetName="tb" Property="Text" Value="&#xECCB;" />
                <Setter TargetName="dp" Property="TextBlock.Foreground" Value="{DynamicResource {x:Static BrushKeys.ForegroundAssist}}" />
                <Setter TargetName="tb_name" Property="Text" Value="已停止" />
            </DataTrigger>
        </DataTemplate.Triggers>
    </DataTemplate>
</ResourceDictionary>