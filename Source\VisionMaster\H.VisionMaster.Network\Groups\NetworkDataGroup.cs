﻿// Copyright (c) HeBianGu Authors. All Rights Reserved. 
// Author: HeBianGu 
// Github: https://github.com/HeBianGu/WPF-Control 
// Document: https://hebiangu.github.io/WPF-Control-Docs  
// QQ:908293466 Group:971261058 
// bilibili: https://space.bilibili.com/370266611 
// Licensed under the MIT License (the "License")

using H.Common.Attributes;
using H.Controls.Diagram.Datas;
using H.Controls.Diagram.Presenter.DiagramDatas;
using H.Controls.Diagram.Presenter.DiagramDatas.Base;
using H.Extensions.Common;
using H.Extensions.FontIcon;
using H.Extensions.Mvvm.ViewModels;
using H.VisionMaster.NodeGroup.Groups.SrcImages;

namespace H.VisionMaster.Network.Groups;

public interface INetwrokDataGroup : INodeDataGroup
{

}
[Icon(FontIcons.NarratorForward)]
[Display(Name = "网络通讯模块", Description = "网络通讯模块", Order = 10700)]
public class NetworkDataGroup : NodeDataGroupBase, IImageDataGroup, INetwrokDataGroup
{
    protected override IEnumerable<INodeData> CreateNodeDatas()
    {
        return typeof(INetwrokNodeData).Assembly.GetInstances<INetwrokNodeData>().OrderBy(x => x.Order);
    }
}

public interface INetwrokNodeData : INodeData, IDisplayBindable
{

}
