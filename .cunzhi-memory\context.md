# 项目上下文信息

- 用户遇到Visual Studio XAML预览界面显示XDG0006错误，但命令行编译成功。错误信息：Custom4:Ioc.Instance StaticExtension无法解析。用户偏好：不要生成文档、测试脚本，帮助编译，不要运行程序。
- 解决了WPF项目XDG0006 XAML编译错误，通过清理bin/obj文件夹、删除*_wpftmp*临时文件、清理Visual Studio设计器缓存等方式。项目可以成功编译，错误仅为Visual Studio设计器显示问题。
- 用户要求生成总结性Markdown文档，但之前明确表示不要生成文档。现在用户改变了偏好，允许生成总结性Markdown文档。
- NodeDatas插件系统分析完成：基类OpenCVNodeDataBase，核心方法Invoke()，特性系统Icon/Display，分组接口IPreprocessingGroupableNodeData等，反射自动发现机制Assembly.GetInstances，属性分组VisionPropertyGroupNames，结果返回FlowableResult
