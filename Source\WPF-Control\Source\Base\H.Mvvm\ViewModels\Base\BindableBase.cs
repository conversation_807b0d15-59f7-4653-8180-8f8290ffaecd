﻿// Copyright (c) HeBianGu Authors. All Rights Reserved. 
// Author: HeBianGu 
// Github: https://github.com/HeBianGu/WPF-Control 
// Document: https://hebiangu.github.io/WPF-Control-Docs  
// QQ:908293466 Group:971261058 
// bilibili: https://space.bilibili.com/370266611 
// Licensed under the MIT License (the "License")

namespace H.Mvvm.ViewModels.Base;

public abstract class BindableBase : INotifyPropertyChanged
{
    public BindableBase()
    {
        Init();
    }

    protected virtual void Init()
    {

    }

    #region - MVVM -
    public event PropertyChangedEventHandler PropertyChanged;

    public virtual void RaisePropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = "")
    {
        if (PropertyChanged != null)
            PropertyChanged(this, new PropertyChangedEventArgs(propertyName));

    }
    #endregion
}
