<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WPF-Control - 强大的WPF控件库</title>
    <style>
        :root {
            --primary-color: #3498db;
            --secondary-color: #2980b9;
            --dark-color: #2c3e50;
            --light-color: #ecf0f1;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            color: #333;
            background-color: #f9f9f9;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        header {
            background-color: var(--dark-color);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: white;
            text-decoration: none;
        }
        
        .nav-links {
            display: flex;
            gap: 20px;
        }
        
        .nav-links a {
            color: white;
            text-decoration: none;
            transition: color 0.3s;
        }
        
        .nav-links a:hover {
            color: var(--primary-color);
        }
        
        .hero {
            background: linear-gradient(135deg, var(--dark-color), var(--secondary-color));
            color: white;
            padding: 60px 0;
            text-align: center;
        }
        
        .hero h1 {
            font-size: 2.5rem;
            margin-bottom: 20px;
        }
        
        .hero p {
            font-size: 1.2rem;
            max-width: 800px;
            margin: 0 auto 30px;
        }
        
        .btn {
            display: inline-block;
            background-color: var(--primary-color);
            color: white;
            padding: 12px 24px;
            border-radius: 4px;
            text-decoration: none;
            font-weight: bold;
            transition: background-color 0.3s;
        }
        
        .btn:hover {
            background-color: var(--secondary-color);
        }
        
        .btn-outline {
            background-color: transparent;
            border: 2px solid white;
            margin-left: 15px;
        }
        
        .btn-outline:hover {
            background-color: rgba(255,255,255,0.1);
        }
        
        section {
            padding: 60px 0;
        }
        
        .section-title {
            text-align: center;
            margin-bottom: 40px;
            color: var(--dark-color);
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }
        
        .feature-card {
            background-color: white;
            border-radius: 8px;
            padding: 25px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
        }
        
        .feature-icon {
            font-size: 2.5rem;
            color: var(--primary-color);
            margin-bottom: 15px;
        }
        
        .feature-card h3 {
            margin-top: 0;
            color: var(--dark-color);
        }
        
        .gallery {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .gallery-item {
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        
        .gallery-item img {
            width: 100%;
            height: auto;
            display: block;
            transition: transform 0.3s;
        }
        
        .gallery-item:hover img {
            transform: scale(1.05);
        }
        
        .tab-container {
            margin-top: 30px;
        }
        
        .tabs {
            display: flex;
            border-bottom: 1px solid #ddd;
            margin-bottom: 20px;
        }
        
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border: 1px solid transparent;
            border-bottom: none;
            border-radius: 4px 4px 0 0;
            margin-right: 5px;
            background-color: #f1f1f1;
        }
        
        .tab.active {
            background-color: white;
            border-color: #ddd;
            border-bottom-color: white;
            color: var(--primary-color);
            font-weight: bold;
        }
        
        .tab-content {
            display: none;
            padding: 20px;
            background-color: white;
            border-radius: 0 0 4px 4px;
            border: 1px solid #ddd;
            border-top: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        footer {
            background-color: var(--dark-color);
            color: white;
            padding: 40px 0 20px;
        }
        
        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .footer-column h3 {
            margin-top: 0;
            margin-bottom: 20px;
            color: var(--primary-color);
        }
        
        .footer-column ul {
            list-style: none;
            padding: 0;
        }
        
        .footer-column ul li {
            margin-bottom: 10px;
        }
        
        .footer-column a {
            color: #bbb;
            text-decoration: none;
            transition: color 0.3s;
        }
        
        .footer-column a:hover {
            color: white;
        }
        
        .social-links {
            display: flex;
            gap: 15px;
        }
        
        .social-links a {
            color: white;
            font-size: 1.5rem;
        }
        
        .copyright {
            text-align: center;
            padding-top: 20px;
            border-top: 1px solid rgba(255,255,255,0.1);
            color: #bbb;
            font-size: 0.9rem;
        }
        
        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }
            
            .hero h1 {
                font-size: 2rem;
            }
            
            .hero p {
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <nav>
                <a href="#" class="logo">WPF-Control</a>
                <div class="nav-links">
                    <a href="#features">功能特性</a>
                    <a href="#gallery">示例展示</a>
                    <a href="#license">许可协议</a>
                    <a href="#contact">联系我们</a>
                    <a href="https://github.com/HeBianGu/WPF-Control" target="_blank">GitHub</a>
                </div>
            </nav>
        </div>
    </header>
    
    <section class="hero">
        <div class="container">
            <h1>强大的WPF控件库</h1>
            <p>WPF-Control 是一个功能丰富的WPF控件集合，提供了多种现代化UI组件和实用工具，帮助开发者快速构建美观、高效的WPF应用程序。</p>
            <div>
                <a href="https://github.com/HeBianGu/WPF-Control" class="btn" target="_blank">GitHub仓库</a>
                <a href="#getting-started" class="btn btn-outline">快速开始</a>
            </div>
        </div>
    </section>
    
    <section id="features">
        <div class="container">
            <h2 class="section-title">功能特性</h2>
            <div class="features">
                <div class="feature-card">
                    <div class="feature-icon">📊</div>
                    <h3>丰富的数据可视化</h3>
                    <p>提供多种图表和图形控件，支持数据绑定和动态更新，满足各种数据展示需求。</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🎨</div>
                    <h3>现代化UI设计</h3>
                    <p>遵循现代化设计语言，提供多种主题和样式，轻松打造专业级应用界面。</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">⚡</div>
                    <h3>高性能渲染</h3>
                    <p>优化渲染性能，确保复杂界面也能流畅运行，提升用户体验。</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🛠️</div>
                    <h3>可扩展架构</h3>
                    <p>模块化设计，易于扩展和定制，满足各种业务场景需求。</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">📱</div>
                    <h3>响应式布局</h3>
                    <p>完美适配不同屏幕尺寸，从桌面到平板都能提供一致的使用体验。</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🔌</div>
                    <h3>MVVM友好</h3>
                    <p>完全支持MVVM模式，提供丰富的命令绑定和交互行为。</p>
                </div>
            </div>
        </div>
    </section>
    
    <section id="gallery" style="background-color: #f5f7fa;">
        <div class="container">
            <h2 class="section-title">示例展示</h2>
            <div class="gallery">
                <div class="gallery-item">
                    <img src="https://raw.githubusercontent.com/HeBianGu/WPF-Control/main/Document/Controls.png" alt="基础控件">
                </div>
                <div class="gallery-item">
                    <img src="https://raw.githubusercontent.com/HeBianGu/WPF-Control/main/Document/NotifyMessage.png" alt="对话消息">
                </div>
                <div class="gallery-item">
                    <img src="https://raw.githubusercontent.com/HeBianGu/WPF-Control/main/Document/Diagram.png" alt="流程图">
                </div>
                <div class="gallery-item">
                    <img src="https://raw.githubusercontent.com/HeBianGu/WPF-Control/main/Document/Guide.png" alt="新手向导">
                </div>
                <div class="gallery-item">
                    <img src="https://raw.githubusercontent.com/HeBianGu/WPF-Control/main/Document/Identify.png" alt="身份认证">
                </div>
                <div class="gallery-item">
                    <img src="https://raw.githubusercontent.com/HeBianGu/WPF-Control/main/Document/Login.png" alt="登录页面">
                </div>
            </div>
        </div>
    </section>
    
    <section id="getting-started">
        <div class="container">
            <h2 class="section-title">快速开始</h2>
            <div class="tab-container">
                <div class="tabs">
                    <div class="tab active" onclick="openTab(event, 'install')">安装</div>
                    <div class="tab" onclick="openTab(event, 'usage')">基本使用</div>
                    <div class="tab" onclick="openTab(event, 'customize')">自定义</div>
                </div>
                
                <div id="install" class="tab-content active">
                    <h3>通过NuGet安装（仅供参考具体以项目示例为准）</h3>
                    <p>在Visual Studio的包管理器控制台中运行以下命令：</p>
                    <pre style="background-color: #f5f5f5; padding: 15px; border-radius: 4px; overflow-x: auto;">
Install-Package WPF-Control</pre>
                    
                    <h3>手动安装（仅供参考具体以项目示例为准）</h3>
                    <p>1. 从GitHub仓库下载最新发布版本</p>
                    <p>2. 添加DLL引用到您的项目中</p>
                    <p>3. 在XAML中添加命名空间引用：</p>
                    <pre style="background-color: #f5f5f5; padding: 15px; border-radius: 4px; overflow-x: auto;">
xmlns:h="clr-namespace:WPF.Control;assembly=WPF.Control"</pre>
                </div>
                
                <div id="usage" class="tab-content">
                    <h3>基本控件使用（仅供参考具体以项目示例为准）</h3>
                    <p>以下是一个简单的按钮控件使用示例：</p>
                    <pre style="background-color: #f5f5f5; padding: 15px; border-radius: 4px; overflow-x: auto;">
&lt;h:CustomButton 
    Content="点击我" 
    Style="{StaticResource PrimaryButtonStyle}"
    Command="{Binding ClickCommand}"/&gt;</pre>
                    
                    <h3>图表控件使用（仅供参考具体以项目示例为准）</h3>
                    <p>以下是一个简单的柱状图示例：</p>
                    <pre style="background-color: #f5f5f5; padding: 15px; border-radius: 4px; overflow-x: auto;">
&lt;h:BarChart 
    ItemsSource="{Binding ChartData}"
    CategoryBinding="Category"
    ValueBinding="Value"/&gt;</pre>
                </div>
                
                <div id="customize" class="tab-content">
                    <h3>自定义样式（仅供参考具体以项目示例为准）</h3>
                    <p>您可以通过覆盖默认样式来自定义控件外观：</p>
                    <pre style="background-color: #f5f5f5; padding: 15px; border-radius: 4px; overflow-x: auto;">
&lt;Style TargetType="{x:Type h:CustomButton}" BasedOn="{StaticResource {x:Type h:CustomButton}}"&gt;
    &lt;Setter Property="Background" Value="#3498db"/&gt;
    &lt;Setter Property="Foreground" Value="White"/&gt;
    &lt;Setter Property="CornerRadius" Value="4"/&gt;
&lt;/Style&gt;</pre>
                    
                    <h3>主题切换（仅供参考具体以项目示例为准）</h3>
                    <p>WPF-Control支持多种内置主题，可以运行时切换：</p>
                    <pre style="background-color: #f5f5f5; padding: 15px; border-radius: 4px; overflow-x: auto;">
// 切换到深色主题
ThemeManager.ApplyTheme(Application.Current, Theme.Dark);

// 切换到浅色主题
ThemeManager.ApplyTheme(Application.Current, Theme.Light);</pre>
                </div>
            </div>
        </div>
    </section>
    
    <section id="license" style="background-color: #f5f7fa;">
        <div class="container">
            <h2 class="section-title">许可协议</h2>
            <div class="tab-container">
                <div class="tabs">
                    <div class="tab active" onclick="openTab(event, 'mit')">MIT许可证</div>
                    <div class="tab" onclick="openTab(event, 'service')">服务协议</div>
                    <div class="tab" onclick="openTab(event, 'privacy')">隐私政策</div>
                </div>
                
                <div id="mit" class="tab-content active">
                    <h3>MIT许可证</h3>
                    <p>版权所有 (c) 2023 HeBianGu</p>
                    <p>特此免费授予任何获得本软件及相关文档文件（以下简称"软件"）副本的人无限制使用软件的权利，包括但不限于使用、复制、修改、合并、发布、分发、再许可和/或销售软件副本的权利，并允许向其提供软件的人这样做，但须符合以下条件：</p>
                    <p>上述版权声明和本许可声明应包含在软件的所有副本或重要部分中。</p>
                    <p>本软件按"原样"提供，不提供任何明示或暗示的担保，包括但不限于对适销性、特定用途适用性和非侵权性的担保。在任何情况下，作者或版权持有人均不对任何索赔、损害或其他责任负责，无论是在合同诉讼、侵权诉讼或其他诉讼中，由软件或软件的使用或其他交易引起、由软件引起或与之相关。</p>
                </div>
                
                <div id="service" class="tab-content">
                    <h3>服务协议</h3>
                    <p>1. <strong>使用条款</strong>：使用本项目即表示您同意遵守MIT许可证的所有条款。</p>
                    <p>2. <strong>免责声明</strong>：本项目按"原样"提供，作者不承担因使用本项目而产生的任何直接或间接责任。</p>
                    <p>3. <strong>贡献指南</strong>：欢迎提交Pull Request，贡献者需确保其贡献代码拥有合法权利。</p>
                    <p>4. <strong>行为准则</strong>：所有项目参与者应遵守开源社区行为准则，保持专业和尊重的沟通。</p>
                    <p>5. <strong>终止条款</strong>：如违反本协议条款，作者有权终止您使用本项目的权利。</p>
                </div>
                
                <div id="privacy" class="tab-content">
                    <h3>隐私政策</h3>
                    <p>1. <strong>数据收集</strong>：本项目本身不收集任何用户数据。但通过GitHub平台使用时，需遵守GitHub的隐私政策。</p>
                    <p>2. <strong>日志信息</strong>：本项目控件不会记录匿名使用数据。</p>
                    <p>3. <strong>Cookies</strong>：本项目网站可能使用必要的Cookies来提供基本功能。</p>
                    <p>4. <strong>第三方服务</strong>：本网站不使用Google Analytics等第三方服务来分析流量，这些服务有自己的隐私政策。</p>
                    <p>5. <strong>政策变更</strong>：我们可能会不定期更新隐私政策，更新后的政策将在本页面发布。</p>
                </div>
            </div>
        </div>
    </section>
    
    <section id="contact">
        <div class="container">
            <h2 class="section-title">联系我们</h2>
            <div class="features">
                <div class="feature-card">
                    <h3>GitHub Issues</h3>
                    <p>报告问题或提出功能请求，请访问我们的GitHub仓库的Issues页面：</p>
                    <a href="https://github.com/HeBianGu/WPF-Control/issues" class="btn" target="_blank">提交Issue</a>
                </div>
                <div class="feature-card">
                    <h3>讨论区</h3>
                    <p>加入我们的GitHub Discussions与其他开发者交流：</p>
                    <a href="https://github.com/HeBianGu/WPF-Control/discussions" class="btn" target="_blank">参与讨论</a>
                </div>
                <div class="feature-card">
                    <h3>电子邮件</h3>
                    <p>如需直接联系项目维护者，请发送邮件至：</p>
                    <a href="mailto:<EMAIL>" class="btn"><EMAIL></a>
                </div>
            </div>
        </div>
    </section>
    
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-column">
                    <h3>关于项目</h3>
                    <ul>
                        <li><a href="#features">功能特性</a></li>
                        <li><a href="#gallery">示例展示</a></li>
                        <li><a href="#getting-started">快速开始</a></li>
                        <li><a href="#license">许可协议</a></li>
                    </ul>
                </div>
                <div class="footer-column">
                    <h3>资源</h3>
                    <ul>
                        <li><a href="https://github.com/HeBianGu/WPF-Control" target="_blank">GitHub仓库</a></li>
                        <li><a href="https://github.com/HeBianGu/WPF-Control/releases" target="_blank">发布版本</a></li>
                        <li><a href="https://github.com/HeBianGu/WPF-Control/wiki" target="_blank">文档Wiki</a></li>
                        <li><a href="https://github.com/HeBianGu/WPF-Control/issues" target="_blank">问题追踪</a></li>
                    </ul>
                </div>
                <div class="footer-column">
                    <h3>社区</h3>
                    <ul>
                        <li><a href="https://github.com/HeBianGu/WPF-Control/discussions" target="_blank">讨论区</a></li>
                        <li><a href="https://github.com/HeBianGu/WPF-Control/blob/master/CONTRIBUTING.md" target="_blank">贡献指南</a></li>
                        <li><a href="https://github.com/HeBianGu/WPF-Control/blob/master/CODE_OF_CONDUCT.md" target="_blank">行为准则</a></li>
                    </ul>
                </div>
                <div class="footer-column">
                    <h3>关注我们</h3>
                    <div class="social-links">
                        <a href="https://github.com/HeBianGu" target="_blank">GitHub</a>
                        <a href="#" target="_blank">Twitter</a>
                        <a href="#" target="_blank">WeChat</a>
                    </div>
                </div>
            </div>
            <div class="copyright">
                <p>© 2023 WPF-Control 项目. 采用 <a href="https://opensource.org/licenses/MIT" target="_blank">MIT许可证</a>.</p>
            </div>
        </div>
    </footer>
    
    <script>
        function openTab(evt, tabName) {
            var i, tabcontent, tablinks;
            
            tabcontent = document.getElementsByClassName("tab-content");
            for (i = 0; i < tabcontent.length; i++) {
                tabcontent[i].classList.remove("active");
            }
            
            tablinks = document.getElementsByClassName("tab");
            for (i = 0; i < tablinks.length; i++) {
                tablinks[i].classList.remove("active");
            }
            
            document.getElementById(tabName).classList.add("active");
            evt.currentTarget.classList.add("active");
        }
        
        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });
    </script>
</body>
</html>