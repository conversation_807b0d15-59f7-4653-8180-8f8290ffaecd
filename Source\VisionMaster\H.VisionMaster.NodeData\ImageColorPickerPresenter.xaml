﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:local="clr-namespace:H.VisionMaster.NodeData">
    <DataTemplate DataType="{x:Type local:ImageColorPickerPresenter}">
        <TextBox Height="Auto"
                 MinHeight="{DynamicResource {x:Static LayoutKeys.ItemHeight}}"
                 Margin="0,0,0,1"
                 VerticalContentAlignment="Center"
                 Cattach.UseGuide="False"
                 Style="{DynamicResource {x:Static TextBoxKeys.Attach}}"
                 Text="{Binding Color}">
            <Cattach.Attach>
                <FontIconButton Command="{Binding ShowImagePickerCommand}"
                                Content="{x:Static FontIcons.Eyedropper}" />
            </Cattach.Attach>
            <Cattach.Title>
                <Border Width="20"
                        Height="10"
                        Background="{Binding Color, Converter={x:Static Converter.GetColorToSolidColorBrush}}" />
            </Cattach.Title>
        </TextBox>
    </DataTemplate>
</ResourceDictionary>