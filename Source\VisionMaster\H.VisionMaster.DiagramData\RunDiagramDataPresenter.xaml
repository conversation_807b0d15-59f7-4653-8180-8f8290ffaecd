﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:b="http://schemas.microsoft.com/xaml/behaviors"
                    xmlns:h="https://github.com/HeBianGu"
                    xmlns:local="clr-namespace:H.VisionMaster.DiagramData">
    <DataTemplate DataType="{x:Type local:RunDiagramDataPresenter}">
        <DockPanel>
            <DockPanel DockPanel.Dock="Bottom"
                       LastChildFill="False">
                <FontIconButton Width="Auto"
                                Height="Auto"
                                Margin="10,20"
                                Padding="60,10"
                                BorderBrush="{DynamicResource {x:Static BrushKeys.BorderBrush}}"
                                BorderThickness="1"
                                Command="{Binding StartCommand}"
                                Content="{x:Static FontIcons.Replay}"
                                FontSize="80" />
                <FontIconButton Width="Auto"
                                Height="Auto"
                                Margin="10,20"
                                Padding="60,10"
                                BorderBrush="{DynamicResource {x:Static BrushKeys.BorderBrush}}"
                                BorderThickness="1"
                                Command="{Binding StartAllCommand}"
                                Content="{x:Static FontIcons.Sync}"
                                FontSize="80" />
                <FontIconButton Width="Auto"
                                Height="Auto"
                                Margin="10,20"
                                Padding="60,10"
                                BorderBrush="{DynamicResource {x:Static BrushKeys.BorderBrush}}"
                                BorderThickness="1"
                                Command="{Binding StopCommand}"
                                Content="&#xE1D2;"
                                FontSize="80" />
                <Border Width="150"
                        Height="100"
                        CornerRadius="5"
                        DockPanel.Dock="Right">
                    <TextBlock HorizontalAlignment="Center"
                               VerticalAlignment="Center"
                               DockPanel.Dock="Right"
                               FontSize="50"
                               Foreground="White">
                        <TextBlock.Style>
                            <Style TargetType="TextBlock">
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding VisionDiagramData.RunModeResult}"
                                                 Value="True">
                                        <Setter Property="Text" Value="OK" />
                                    </DataTrigger>
                                    <DataTrigger Binding="{Binding VisionDiagramData.RunModeResult}"
                                                 Value="False">
                                        <Setter Property="Text" Value="NG" />
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </TextBlock.Style>
                    </TextBlock>
                    <Border.Style>
                        <Style TargetType="Border">
                            <Setter Property="Background" Value="{DynamicResource {x:Static BrushKeys.Gray}}" />
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding VisionDiagramData.RunModeResult}"
                                             Value="True">
                                    <Setter Property="Background" Value="{DynamicResource {x:Static BrushKeys.Green}}" />
                                </DataTrigger>
                                <DataTrigger Binding="{Binding VisionDiagramData.RunModeResult}"
                                             Value="False">
                                    <Setter Property="Background" Value="{DynamicResource {x:Static BrushKeys.Red}}" />
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </Border.Style>
                </Border>
            </DockPanel>
            <h:Zoombox x:Name="zoomview1"
                       Background="{DynamicResource {x:Static BrushKeys.Tile25}}"
                       DragModifiers=""
                       Focusable="True"
                       IsTabStop="True"
                       MinScale="0.2"
                       NavigateOnPreview="False"
                       RelativeZoomModifiers=""
                       ViewStackIndex="0"
                       ZoomOn="Content">
                <b:Interaction.Behaviors>
                    <h:ZoomBoxFitOnLoadedBehavior />
                    <h:ZoomBoxFitOnSizeChangedBehavior />
                </b:Interaction.Behaviors>
                <h:Zoombox.ViewStack>
                    <h:ZoomboxView>Fit</h:ZoomboxView>
                </h:Zoombox.ViewStack>
                <b:Interaction.Triggers>
                    <h:MouseTrigger ClickCount="2"
                                    Mode="Right"
                                    MouseButton="Left"
                                    UseHandle="False">
                        <h:CallMethodActionEx MethodName="FitToBounds"
                                              TargetObject="{Binding ElementName=zoomview1}" />
                    </h:MouseTrigger>
                    <h:MouseTrigger ClickCount="2"
                                    Mode="Left"
                                    MouseButton="Left"
                                    UseHandle="False">
                        <h:CallMethodActionEx MethodName="FitToBounds"
                                              TargetObject="{Binding ElementName=zoomview1}" />
                    </h:MouseTrigger>
                    <b:EventTrigger EventName="MouseDoubleClick">
                        <b:InvokeCommandAction Command="{ShowZoomViewImageCommand}"
                                               CommandParameter="{Binding VisionDiagramData.ResultImageSource}" />
                    </b:EventTrigger>
                </b:Interaction.Triggers>
                <Image MinWidth="100"
                       MinHeight="100">
                    <b:Interaction.Triggers>
                        <b:EventTrigger EventName="SourceUpdated">
                            <h:CallMethodActionEx MethodName="FitToBounds"
                                                  TargetObject="{Binding ElementName=zoomview1}" />
                        </b:EventTrigger>
                        <b:EventTrigger EventName="TargetUpdated">
                            <h:CallMethodActionEx MethodName="FitToBounds"
                                                  TargetObject="{Binding ElementName=zoomview1}" />
                        </b:EventTrigger>
                    </b:Interaction.Triggers>
                    <Image.Style>
                        <Style TargetType="Image">
                            <Setter Property="Source" Value="{Binding VisionDiagramData.ResultImageSource, NotifyOnSourceUpdated=True, NotifyOnTargetUpdated=True}" />
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding VisionDiagramData.ResultImageSource}"
                                             Value="{x:Null}">
                                    <Setter Property="Source" Value="{Binding VisionDiagramData.SelectedImageData.FilePath}" />
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </Image.Style>
                </Image>
            </h:Zoombox>
        </DockPanel>
    </DataTemplate>
</ResourceDictionary>