# NodeDatas 插件构建模式分析与开发指南

## 📋 目录
- [1. 现有 NodeData 插件的共性分析](#1-现有-nodedata-插件的共性分析)
- [2. 插件的发现与生命周期](#2-插件的发现与生命周期)
- [3. 分步指南：创建新的 OpenCV 节点插件](#3-分步指南创建新的-opencv-节点插件)

---

## 1. 现有 NodeData 插件的共性分析

### 1.1 基类或接口继承结构

所有 OpenCV 节点插件都遵循以下继承层次结构：

```
OpenCVNodeDataBase (抽象基类)
    ↓ 继承自
SelectableResultImageNodeData<Mat>
    ↓ 继承自  
ROINodeData<T>
    ↓ 继承自
VisionNodeData<T>
    ↓ 继承自
DemoNodeDataBase
```

**核心基类分析**：

<augment_code_snippet path="Source\VisionMaster\H.VisionMaster.OpenCV\Base\OpenCVNodeDataBase.cs" mode="EXCERPT">
````csharp
public abstract class OpenCVNodeDataBase : SelectableResultImageNodeData<Mat>, IOpenCVNodeData
{
    protected override bool IsValid(Mat t)
    {
        return t.IsValid();
    }
    protected override void UpdateResultImageSource()
    {
        this.UpdateResultImageSource(this.Mat);
    }

    protected virtual void UpdateResultImageSource(Mat mat)
    {
        this.ResultImageSource = mat.ToImageSource();
    }
}
````
</augment_code_snippet>

### 1.2 核心属性 (Properties)

所有插件类都具备以下核心属性：

- **`Mat`**: 当前节点处理的 OpenCV 图像矩阵
- **`ResultImageSource`**: 用于 UI 显示的图像源
- **`ResultImages`**: 结果图像集合，支持多输出
- **`UseInvokedPart`**: 控制是否输出到历史记录和预览
- **`ROI`**: 感兴趣区域设置
- **`SelectedResultImage`**: 选择的输入图像源

### 1.3 构造函数模式

大多数节点使用默认构造函数，复杂节点会重写 `LoadDefault()` 方法：

<augment_code_snippet path="Source\VisionMaster\H.VisionMaster.OpenCV\NodeDatas\3 - Blurs\GaussianBlur.cs" mode="EXCERPT">
````csharp
public override void LoadDefault()
{
    base.LoadDefault();
    this.KSize = new System.Windows.Size(7, 7);
}
````
</augment_code_snippet>

### 1.4 核心方法 (Methods)

**必须实现的抽象方法**：

<augment_code_snippet path="Source\VisionMaster\H.VisionMaster.NodeData\Base\VisionNodeData.cs" mode="EXCERPT">
````csharp
protected abstract FlowableResult<T> Invoke(ISrcVisionNodeData<T> srcImageNodeData, IVisionNodeData<T> from, IFlowableDiagramData diagram);
````
</augment_code_snippet>

**典型实现模式**：

<augment_code_snippet path="Source\VisionMaster\H.VisionMaster.OpenCV\NodeDatas\2 - Preprocessings\CvtColor.cs" mode="EXCERPT">
````csharp
protected override FlowableResult<Mat> Invoke(ISrcVisionNodeData<Mat> srcImageNodeData, IVisionNodeData<Mat> from, IFlowableDiagramData diagram)
{
    Mat mat = from.Mat.CvtColor(this.ColorConversionCode, this.DstCn);
    return this.OK(mat);
}
````
</augment_code_snippet>

### 1.5 C# 特性 (Attributes)

**必需特性**：

1. **`[Icon]`** - 定义节点图标
2. **`[Display]`** - 定义节点显示信息

<augment_code_snippet path="Source\VisionMaster\H.VisionMaster.OpenCV\NodeDatas\2 - Preprocessings\CvtColor.cs" mode="EXCERPT">
````csharp
[Icon(FontIcons.Color)]
[Display(Name = "色彩变换", GroupName = "基础函数", Description = "设置图片颜色", Order = 2)]
public class CvtColor : OpenCVNodeDataBase, IPreprocessingGroupableNodeData
````
</augment_code_snippet>

**属性特性**：

<augment_code_snippet path="Source\VisionMaster\H.VisionMaster.OpenCV\NodeDatas\2 - Preprocessings\CvtColor.cs" mode="EXCERPT">
````csharp
[DefaultValue(ColorConversionCodes.BGR2GRAY)]
[Display(Name = "转换规则", GroupName = VisionPropertyGroupNames.RunParameters)]
public ColorConversionCodes ColorConversionCode
{
    get { return _colorConversionCode; }
    set
    {
        _colorConversionCode = value;
        RaisePropertyChanged();
        this.UpdateInvokeCurrent();
    }
}
````
</augment_code_snippet>

---

## 2. 插件的发现与生命周期

### 2.1 插件发现机制

系统使用**反射扫描**自动发现插件：

<augment_code_snippet path="Source\WPF-Control\Source\Extensions\H.Extensions.Common\Extension.Assembly.cs" mode="EXCERPT">
````csharp
public static IEnumerable<T> GetInstances<T>(this Assembly assembly)
{
    var types = assembly.GetTypes();
    types = types.Where(t => t.IsClass && !t.IsAbstract).ToArray();
    types = types.Where(t => typeof(T).IsAssignableFrom(t)).ToArray();
    return types.Select(t => Activator.CreateInstance(t)).OfType<T>();
}
````
</augment_code_snippet>

### 2.2 分组注册机制

每个节点组通过 `CreateNodeDatas()` 方法自动发现对应接口的实现：

<augment_code_snippet path="Source\VisionMaster\H.VisionMaster.NodeGroup\Groups\Preprocessings\PreprocessingDataGroup.cs" mode="EXCERPT">
````csharp
protected override IEnumerable<INodeData> CreateNodeDatas()
{
    return this.GetType().Assembly.GetInstances<IPreprocessingGroupableNodeData>().OrderBy(x => x.Order);
}
````
</augment_code_snippet>

### 2.3 实例化与使用

1. **启动时扫描**：应用启动时扫描所有程序集
2. **分组注册**：按接口类型自动分组注册
3. **用户拖拽**：用户从工具箱拖拽节点到画布时创建实例
4. **流程执行**：按连接顺序调用 `Invoke()` 方法

---

## 3. 分步指南：创建新的 OpenCV 节点插件

### 步骤 1: 创建类文件

在 `Source\VisionMaster\H.VisionMaster.OpenCV\NodeDatas\2 - Preprocessings` 目录下创建 `GrayscaleNodeData.cs`：

### 步骤 2: 继承基类并定义元数据

```csharp
using H.VisionMaster.NodeData;
using H.VisionMaster.NodeGroup.Groups.Preprocessings;

namespace H.VisionMaster.OpenCV.NodeDatas.Basic;

[Icon(FontIcons.Color)]
[Display(Name = "图像灰度化", GroupName = "基础函数", Description = "将彩色图像转换为灰度图像", Order = 1)]
public class GrayscaleNodeData : OpenCVNodeDataBase, IPreprocessingGroupableNodeData
{
    // 实现内容...
}
```

### 步骤 3: 实现构造函数

```csharp
public override void LoadDefault()
{
    base.LoadDefault();
    // 设置默认参数值
}
```

### 步骤 4: 实现核心执行逻辑

```csharp
protected override FlowableResult<Mat> Invoke(ISrcVisionNodeData<Mat> srcImageNodeData, IVisionNodeData<Mat> from, IFlowableDiagramData diagram)
{
    try
    {
        // 1. 获取输入图像
        Mat inputMat = from.Mat;
        if (inputMat == null || inputMat.Empty())
            return this.Error(null, "输入图像为空");

        // 2. 执行灰度化处理
        Mat grayMat = new Mat();
        Cv2.CvtColor(inputMat, grayMat, ColorConversionCodes.BGR2GRAY);

        // 3. 返回处理结果
        return this.OK(grayMat, "灰度化处理完成");
    }
    catch (Exception ex)
    {
        return this.Error(null, $"灰度化处理失败: {ex.Message}");
    }
}
```

### 步骤 5: 完整示例代码

```csharp
// Copyright (c) HeBianGu Authors. All Rights Reserved. 
// Licensed under the MIT License (the "License")

using H.VisionMaster.NodeData;
using H.VisionMaster.NodeGroup.Groups.Preprocessings;

namespace H.VisionMaster.OpenCV.NodeDatas.Basic;

/// <summary>
/// 图像灰度化节点 - 将彩色图像转换为灰度图像
/// </summary>
[Icon(FontIcons.Color)]
[Display(Name = "图像灰度化", GroupName = "基础函数", Description = "将彩色图像转换为灰度图像，减少数据维度便于后续处理", Order = 1)]
public class GrayscaleNodeData : OpenCVNodeDataBase, IPreprocessingGroupableNodeData
{
    /// <summary>
    /// 核心处理方法：执行图像灰度化
    /// </summary>
    /// <param name="srcImageNodeData">源图像节点数据</param>
    /// <param name="from">输入节点数据</param>
    /// <param name="diagram">流程图数据</param>
    /// <returns>处理结果</returns>
    protected override FlowableResult<Mat> Invoke(ISrcVisionNodeData<Mat> srcImageNodeData, IVisionNodeData<Mat> from, IFlowableDiagramData diagram)
    {
        try
        {
            // 1. 验证输入数据
            Mat inputMat = from.Mat;
            if (inputMat == null || inputMat.Empty())
                return this.Error(null, "输入图像为空，请检查上游节点");

            // 2. 检查图像是否已经是灰度图
            if (inputMat.Channels() == 1)
                return this.OK(inputMat.Clone(), "图像已经是灰度图，直接输出");

            // 3. 执行彩色到灰度的转换
            Mat grayMat = new Mat();
            Cv2.CvtColor(inputMat, grayMat, ColorConversionCodes.BGR2GRAY);

            // 4. 返回成功结果
            return this.OK(grayMat, $"灰度化完成，输出尺寸: {grayMat.Width}x{grayMat.Height}");
        }
        catch (Exception ex)
        {
            // 5. 异常处理
            return this.Error(null, $"灰度化处理失败: {ex.Message}");
        }
    }
}
```

## 🎯 关键要点总结

1. **继承结构**：必须继承 `OpenCVNodeDataBase` 基类
2. **分组接口**：实现对应的分组接口（如 `IPreprocessingGroupableNodeData`）
3. **特性标注**：使用 `[Icon]` 和 `[Display]` 特性定义UI显示
4. **核心方法**：重写 `Invoke()` 方法实现处理逻辑
5. **异常处理**：使用 `try-catch` 并返回适当的错误信息
6. **结果返回**：使用 `this.OK()` 或 `this.Error()` 返回结果
7. **自动发现**：无需手动注册，系统会自动发现并加载插件

## 🔧 高级开发技巧

### 4.1 属性分组规范

使用 `VisionPropertyGroupNames` 中定义的标准分组：

<augment_code_snippet path="Source\VisionMaster\H.VisionMaster.NodeData\VisionPropertyGroupNames.cs" mode="EXCERPT">
````csharp
public class VisionPropertyGroupNames
{
    public const string RunParameters = "运行参数";
    public const string BaseParameters = "基本参数";
    public const string ResultParameters = "结果参数";
    public const string FlowParameters = "流程控制";
    public const string DisplayParameters = "显示参数";
    public const string OtherParameters = "其他参数";
}
````
</augment_code_snippet>

### 4.2 可用的分组接口

| 分组接口 | 用途 | 示例节点 |
|---------|------|----------|
| `IPreprocessingGroupableNodeData` | 图像预处理 | 色彩变换、旋转、缩放 |
| `IBlurGroupableNodeData` | 滤波模糊 | 高斯滤波、均值滤波 |
| `IMorphologyGroupableNodeData` | 形态学操作 | 腐蚀、膨胀、开闭运算 |
| `IDetectorGroupableNodeData` | 对象检测 | 边缘检测、轮廓检测 |
| `IFeatureDetectorOpenCVNodeData` | 特征检测 | SIFT、SURF、ORB |
| `ITemplateMatchingGroupableNodeData` | 模板匹配 | 模板匹配、特征匹配 |
| `IOtherGroupableNodeData` | 其他功能 | 直方图、HOG特征 |
| `IOutputGroupableNodeData` | 结果输出 | OK/NG输出、消息提示 |

### 4.3 参数属性的高级配置

**数值滑块属性**：
```csharp
private double _threshold = 127.0;
[PropertyItem(typeof(DoubleSliderTextPropertyItem))]
[Range(0.0, 255.0)]
[DefaultValue(127.0)]
[Display(Name = "阈值", GroupName = VisionPropertyGroupNames.RunParameters, Description = "二值化阈值，范围0-255")]
public double Threshold
{
    get { return _threshold; }
    set
    {
        _threshold = value;
        RaisePropertyChanged();
        this.UpdateInvokeCurrent(); // 实时更新预览
    }
}
```

**枚举选择属性**：
```csharp
private ThresholdTypes _thresholdType = ThresholdTypes.Binary;
[DefaultValue(ThresholdTypes.Binary)]
[Display(Name = "阈值类型", GroupName = VisionPropertyGroupNames.RunParameters)]
public ThresholdTypes ThresholdType
{
    get { return _thresholdType; }
    set
    {
        _thresholdType = value;
        RaisePropertyChanged();
        this.UpdateInvokeCurrent();
    }
}
```

**尺寸属性**：
```csharp
private System.Windows.Size _kernelSize = new System.Windows.Size(3, 3);
[Display(Name = "核大小", GroupName = VisionPropertyGroupNames.RunParameters)]
public System.Windows.Size KernelSize
{
    get { return _kernelSize; }
    set
    {
        _kernelSize = value;
        RaisePropertyChanged();
        this.UpdateInvokeCurrent();
    }
}
```

**只读结果属性**：
```csharp
private int _detectedCount;
[ReadOnly(true)]
[Display(Name = "检测数量", GroupName = VisionPropertyGroupNames.ResultParameters, Description = "检测到的对象数量")]
public int DetectedCount
{
    get { return _detectedCount; }
    set
    {
        _detectedCount = value;
        RaisePropertyChanged();
    }
}
```

### 4.4 错误处理最佳实践

```csharp
protected override FlowableResult<Mat> Invoke(ISrcVisionNodeData<Mat> srcImageNodeData, IVisionNodeData<Mat> from, IFlowableDiagramData diagram)
{
    try
    {
        // 1. 输入验证
        if (from?.Mat == null)
            return this.Error(null, "输入图像为空");

        Mat inputMat = from.Mat;
        if (inputMat.Empty())
            return this.Error(null, "输入图像数据无效");

        // 2. 参数验证
        if (this.KernelSize.Width <= 0 || this.KernelSize.Height <= 0)
            return this.Error(inputMat, "核大小必须大于0");

        // 3. 处理逻辑
        Mat result = new Mat();
        // ... OpenCV 处理代码 ...

        // 4. 结果验证
        if (result.Empty())
            return this.Error(inputMat, "处理结果为空");

        return this.OK(result, "处理成功");
    }
    catch (OpenCVException cvEx)
    {
        return this.Error(from?.Mat, $"OpenCV错误: {cvEx.Message}");
    }
    catch (ArgumentException argEx)
    {
        return this.Error(from?.Mat, $"参数错误: {argEx.Message}");
    }
    catch (Exception ex)
    {
        return this.Error(from?.Mat, $"未知错误: {ex.Message}");
    }
}
```

### 4.5 多输出结果支持

```csharp
protected override IEnumerable<IVisionResultImage<Mat>> GetResultImages()
{
    yield return new VisionResultImage<Mat>() { Name = this.Name + " - 原图", Image = this.OriginalMat };
    yield return new VisionResultImage<Mat>() { Name = this.Name + " - 处理结果", Image = this.Mat };
    if (this.MaskMat != null)
        yield return new VisionResultImage<Mat>() { Name = this.Name + " - 掩码", Image = this.MaskMat };
}
```

## 📚 扩展阅读

- 查看 `Source\VisionMaster\H.VisionMaster.OpenCV\NodeDatas` 下的其他示例
- 参考 `VisionPropertyGroupNames` 了解属性分组规范
- 学习 `FlowableResult<Mat>` 的高级用法
- 了解 ROI（感兴趣区域）的使用方法
- 研究现有节点的 `LoadDefault()` 方法实现
- 学习如何使用 `UpdateInvokeCurrent()` 实现实时预览

## 🚀 快速开始模板

创建新节点的最小化模板：

```csharp
[Icon(FontIcons.YourIcon)]
[Display(Name = "您的节点名", GroupName = "分组名", Description = "功能描述", Order = 1)]
public class YourNodeData : OpenCVNodeDataBase, IPreprocessingGroupableNodeData
{
    protected override FlowableResult<Mat> Invoke(ISrcVisionNodeData<Mat> srcImageNodeData, IVisionNodeData<Mat> from, IFlowableDiagramData diagram)
    {
        try
        {
            Mat input = from.Mat;
            if (input?.Empty() != false)
                return this.Error(null, "输入无效");

            // 您的处理逻辑
            Mat output = input.Clone(); // 替换为实际处理

            return this.OK(output, "处理完成");
        }
        catch (Exception ex)
        {
            return this.Error(from?.Mat, $"处理失败: {ex.Message}");
        }
    }
}
```

## 🔄 异步处理支持

对于耗时较长的处理操作，可以重写异步方法：

```csharp
public override async Task<IFlowableResult> InvokeAsync(IFlowableLinkData previors, IFlowableDiagramData diagram)
{
    return await Task.Run(() =>
    {
        // 获取输入数据
        ISrcVisionNodeData<Mat> srcData = diagram.GetStartNodeDatas().OfType<ISrcVisionNodeData<Mat>>().FirstOrDefault();
        IVisionNodeData<Mat> fromData = this.GetFromNodeData<IVisionNodeData<Mat>>(diagram, previors);

        // 执行长时间处理
        return this.InvokeAction(() => this.Invoke(srcData, fromData ?? srcData, diagram));
    });
}
```

## 🧪 测试与调试指导

### 5.1 单元测试模板

```csharp
[TestClass]
public class GrayscaleNodeDataTests
{
    [TestMethod]
    public void TestGrayscaleConversion()
    {
        // Arrange
        var node = new GrayscaleNodeData();
        var inputMat = new Mat(100, 100, MatType.CV_8UC3, Scalar.All(128));
        var mockSrcData = new Mock<ISrcVisionNodeData<Mat>>();
        var mockFromData = new Mock<IVisionNodeData<Mat>>();
        var mockDiagram = new Mock<IFlowableDiagramData>();

        mockFromData.Setup(x => x.Mat).Returns(inputMat);

        // Act
        var result = node.Invoke(mockSrcData.Object, mockFromData.Object, mockDiagram.Object);

        // Assert
        Assert.AreEqual(FlowableResultState.OK, result.State);
        Assert.IsNotNull(result.Value);
        Assert.AreEqual(1, result.Value.Channels()); // 验证是灰度图
    }

    [TestMethod]
    public void TestEmptyInputHandling()
    {
        // Arrange
        var node = new GrayscaleNodeData();
        var mockSrcData = new Mock<ISrcVisionNodeData<Mat>>();
        var mockFromData = new Mock<IVisionNodeData<Mat>>();
        var mockDiagram = new Mock<IFlowableDiagramData>();

        mockFromData.Setup(x => x.Mat).Returns((Mat)null);

        // Act
        var result = node.Invoke(mockSrcData.Object, mockFromData.Object, mockDiagram.Object);

        // Assert
        Assert.AreEqual(FlowableResultState.Error, result.State);
        Assert.IsTrue(result.Message.Contains("输入图像为空"));
    }
}
```

### 5.2 调试技巧

**1. 启用详细日志**：
```csharp
protected override FlowableResult<Mat> Invoke(ISrcVisionNodeData<Mat> srcImageNodeData, IVisionNodeData<Mat> from, IFlowableDiagramData diagram)
{
    this.Logger?.Info($"开始处理: {this.Name}");
    this.Logger?.Info($"输入图像尺寸: {from.Mat?.Width}x{from.Mat?.Height}");

    // 处理逻辑...

    this.Logger?.Info($"处理完成: {result.Width}x{result.Height}");
    return this.OK(result);
}
```

**2. 中间结果保存**：
```csharp
// 调试时保存中间结果
if (System.Diagnostics.Debugger.IsAttached)
{
    intermediateMat.SaveImage($"debug_{this.Name}_{DateTime.Now:yyyyMMdd_HHmmss}.png");
}
```

**3. 性能监控**：
```csharp
protected override FlowableResult<Mat> Invoke(ISrcVisionNodeData<Mat> srcImageNodeData, IVisionNodeData<Mat> from, IFlowableDiagramData diagram)
{
    var stopwatch = System.Diagnostics.Stopwatch.StartNew();

    try
    {
        // 处理逻辑...
        var result = this.OK(outputMat);

        stopwatch.Stop();
        this.Logger?.Info($"处理耗时: {stopwatch.ElapsedMilliseconds}ms");

        return result;
    }
    catch (Exception ex)
    {
        stopwatch.Stop();
        this.Logger?.Error($"处理失败，耗时: {stopwatch.ElapsedMilliseconds}ms, 错误: {ex.Message}");
        throw;
    }
}
```

## 📋 开发检查清单

在发布新节点前，请确认以下项目：

### ✅ 基础要求
- [ ] 继承自 `OpenCVNodeDataBase`
- [ ] 实现对应的分组接口
- [ ] 添加 `[Icon]` 和 `[Display]` 特性
- [ ] 重写 `Invoke()` 方法
- [ ] 包含适当的异常处理

### ✅ 代码质量
- [ ] 输入验证完整
- [ ] 错误消息清晰明确
- [ ] 内存管理正确（Mat对象的释放）
- [ ] 参数范围验证
- [ ] 性能优化合理

### ✅ 用户体验
- [ ] 属性分组合理
- [ ] 参数描述清晰
- [ ] 默认值设置合适
- [ ] 支持实时预览（`UpdateInvokeCurrent()`）
- [ ] 结果消息有意义

### ✅ 测试覆盖
- [ ] 正常流程测试
- [ ] 边界条件测试
- [ ] 异常情况测试
- [ ] 性能测试
- [ ] 内存泄漏测试

## 🎯 常见问题解答

**Q: 如何处理多输入节点？**
A: 使用 `this.GetFromNodeDatas<IVisionNodeData<Mat>>()` 获取所有输入节点。

**Q: 如何添加自定义属性编辑器？**
A: 使用 `[PropertyItem(typeof(YourCustomPropertyItem))]` 特性。

**Q: 如何实现条件分支？**
A: 返回不同的 `FlowableResult<T>` 类型，配合条件连接线使用。

**Q: 如何优化大图像处理性能？**
A: 考虑使用 ROI 处理、多线程、或异步方法。

**Q: 如何处理不同图像格式？**
A: 在 `Invoke()` 方法开始时检查并转换图像格式。

## 💡 实际案例集合

### 案例1：自适应阈值二值化节点

基于现有的 `Threshold.cs` 扩展，创建自适应阈值处理：

```csharp
[Icon(FontIcons.Contrast)]
[Display(Name = "自适应阈值", GroupName = "图像预处理", Description = "根据局部区域自动计算阈值进行二值化", Order = 5)]
public class AdaptiveThresholdNodeData : OpenCVNodeDataBase, IPreprocessingGroupableNodeData
{
    public override void LoadDefault()
    {
        base.LoadDefault();
        this.MaxValue = 255;
        this.AdaptiveMethod = AdaptiveThresholdTypes.MeanC;
        this.ThresholdType = ThresholdTypes.Binary;
        this.BlockSize = 11;
        this.C = 2;
    }

    private double _maxValue = 255;
    [PropertyItem(typeof(DoubleSliderTextPropertyItem))]
    [Range(0, 255)]
    [DefaultValue(255)]
    [Display(Name = "最大值", GroupName = VisionPropertyGroupNames.RunParameters)]
    public double MaxValue
    {
        get { return _maxValue; }
        set { _maxValue = value; RaisePropertyChanged(); this.UpdateInvokeCurrent(); }
    }

    private AdaptiveThresholdTypes _adaptiveMethod = AdaptiveThresholdTypes.MeanC;
    [DefaultValue(AdaptiveThresholdTypes.MeanC)]
    [Display(Name = "自适应方法", GroupName = VisionPropertyGroupNames.RunParameters)]
    public AdaptiveThresholdTypes AdaptiveMethod
    {
        get { return _adaptiveMethod; }
        set { _adaptiveMethod = value; RaisePropertyChanged(); this.UpdateInvokeCurrent(); }
    }

    private ThresholdTypes _thresholdType = ThresholdTypes.Binary;
    [DefaultValue(ThresholdTypes.Binary)]
    [Display(Name = "阈值类型", GroupName = VisionPropertyGroupNames.RunParameters)]
    public ThresholdTypes ThresholdType
    {
        get { return _thresholdType; }
        set { _thresholdType = value; RaisePropertyChanged(); this.UpdateInvokeCurrent(); }
    }

    private int _blockSize = 11;
    [PropertyItem(typeof(IntegerSliderTextPropertyItem))]
    [Range(3, 99)]
    [DefaultValue(11)]
    [Display(Name = "块大小", GroupName = VisionPropertyGroupNames.RunParameters, Description = "计算阈值的邻域大小，必须为奇数")]
    public int BlockSize
    {
        get { return _blockSize; }
        set
        {
            // 确保为奇数
            _blockSize = value % 2 == 0 ? value + 1 : value;
            RaisePropertyChanged();
            this.UpdateInvokeCurrent();
        }
    }

    private double _c = 2;
    [PropertyItem(typeof(DoubleSliderTextPropertyItem))]
    [Range(-10, 10)]
    [DefaultValue(2)]
    [Display(Name = "常数C", GroupName = VisionPropertyGroupNames.RunParameters, Description = "从平均值中减去的常数")]
    public double C
    {
        get { return _c; }
        set { _c = value; RaisePropertyChanged(); this.UpdateInvokeCurrent(); }
    }

    protected override FlowableResult<Mat> Invoke(ISrcVisionNodeData<Mat> srcImageNodeData, IVisionNodeData<Mat> from, IFlowableDiagramData diagram)
    {
        try
        {
            Mat src = from.Mat;
            if (src?.Empty() != false)
                return this.Error(null, "输入图像为空");

            // 转换为灰度图（如果需要）
            Mat grayMat = src.Channels() == 1 ? src : src.CvtColor(ColorConversionCodes.BGR2GRAY);

            Mat result = new Mat();
            Cv2.AdaptiveThreshold(grayMat, result, this.MaxValue, this.AdaptiveMethod, this.ThresholdType, this.BlockSize, this.C);

            // 如果创建了临时灰度图，需要释放
            if (grayMat != src)
                grayMat.Dispose();

            return this.OK(result, $"自适应阈值完成，块大小: {this.BlockSize}");
        }
        catch (Exception ex)
        {
            return this.Error(from?.Mat, $"自适应阈值失败: {ex.Message}");
        }
    }
}
```

### 案例2：边缘检测组合节点

结合多种边缘检测算法的复合节点：

```csharp
[Icon(FontIcons.Edit)]
[Display(Name = "多算法边缘检测", GroupName = "特征检测", Description = "结合Canny、Sobel、Laplacian多种算法的边缘检测", Order = 1)]
public class MultiEdgeDetectorNodeData : OpenCVNodeDataBase, IDetectorGroupableNodeData
{
    public enum EdgeDetectionMethod
    {
        [Display(Name = "Canny算法")] Canny,
        [Display(Name = "Sobel算子")] Sobel,
        [Display(Name = "Laplacian算子")] Laplacian,
        [Display(Name = "组合算法")] Combined
    }

    public override void LoadDefault()
    {
        base.LoadDefault();
        this.Method = EdgeDetectionMethod.Canny;
        this.CannyThreshold1 = 50;
        this.CannyThreshold2 = 150;
        this.SobelKSize = 3;
        this.LaplacianKSize = 1;
    }

    private EdgeDetectionMethod _method = EdgeDetectionMethod.Canny;
    [Display(Name = "检测方法", GroupName = VisionPropertyGroupNames.RunParameters)]
    public EdgeDetectionMethod Method
    {
        get { return _method; }
        set { _method = value; RaisePropertyChanged(); this.UpdateInvokeCurrent(); }
    }

    private double _cannyThreshold1 = 50;
    [PropertyItem(typeof(DoubleSliderTextPropertyItem))]
    [Range(0, 255)]
    [Display(Name = "Canny低阈值", GroupName = VisionPropertyGroupNames.RunParameters)]
    public double CannyThreshold1
    {
        get { return _cannyThreshold1; }
        set { _cannyThreshold1 = value; RaisePropertyChanged(); this.UpdateInvokeCurrent(); }
    }

    private double _cannyThreshold2 = 150;
    [PropertyItem(typeof(DoubleSliderTextPropertyItem))]
    [Range(0, 255)]
    [Display(Name = "Canny高阈值", GroupName = VisionPropertyGroupNames.RunParameters)]
    public double CannyThreshold2
    {
        get { return _cannyThreshold2; }
        set { _cannyThreshold2 = value; RaisePropertyChanged(); this.UpdateInvokeCurrent(); }
    }

    private int _sobelKSize = 3;
    [PropertyItem(typeof(IntegerComboBoxPropertyItem))]
    [ItemsSource(typeof(KernelSizeItemsSource))] // 1,3,5,7
    [Display(Name = "Sobel核大小", GroupName = VisionPropertyGroupNames.RunParameters)]
    public int SobelKSize
    {
        get { return _sobelKSize; }
        set { _sobelKSize = value; RaisePropertyChanged(); this.UpdateInvokeCurrent(); }
    }

    private int _laplacianKSize = 1;
    [PropertyItem(typeof(IntegerComboBoxPropertyItem))]
    [ItemsSource(typeof(KernelSizeItemsSource))]
    [Display(Name = "Laplacian核大小", GroupName = VisionPropertyGroupNames.RunParameters)]
    public int LaplacianKSize
    {
        get { return _laplacianKSize; }
        set { _laplacianKSize = value; RaisePropertyChanged(); this.UpdateInvokeCurrent(); }
    }

    private int _edgeCount;
    [ReadOnly(true)]
    [Display(Name = "边缘像素数", GroupName = VisionPropertyGroupNames.ResultParameters)]
    public int EdgeCount
    {
        get { return _edgeCount; }
        set { _edgeCount = value; RaisePropertyChanged(); }
    }

    protected override FlowableResult<Mat> Invoke(ISrcVisionNodeData<Mat> srcImageNodeData, IVisionNodeData<Mat> from, IFlowableDiagramData diagram)
    {
        try
        {
            Mat src = from.Mat;
            if (src?.Empty() != false)
                return this.Error(null, "输入图像为空");

            // 转换为灰度图
            Mat gray = src.Channels() == 1 ? src.Clone() : src.CvtColor(ColorConversionCodes.BGR2GRAY);
            Mat result = new Mat();

            switch (this.Method)
            {
                case EdgeDetectionMethod.Canny:
                    Cv2.Canny(gray, result, this.CannyThreshold1, this.CannyThreshold2);
                    break;

                case EdgeDetectionMethod.Sobel:
                    Mat sobelX = new Mat(), sobelY = new Mat();
                    Cv2.Sobel(gray, sobelX, MatType.CV_16S, 1, 0, this.SobelKSize);
                    Cv2.Sobel(gray, sobelY, MatType.CV_16S, 0, 1, this.SobelKSize);
                    Cv2.ConvertScaleAbs(sobelX, sobelX);
                    Cv2.ConvertScaleAbs(sobelY, sobelY);
                    Cv2.AddWeighted(sobelX, 0.5, sobelY, 0.5, 0, result);
                    sobelX.Dispose();
                    sobelY.Dispose();
                    break;

                case EdgeDetectionMethod.Laplacian:
                    Mat laplacian = new Mat();
                    Cv2.Laplacian(gray, laplacian, MatType.CV_16S, this.LaplacianKSize);
                    Cv2.ConvertScaleAbs(laplacian, result);
                    laplacian.Dispose();
                    break;

                case EdgeDetectionMethod.Combined:
                    // 组合多种算法
                    Mat canny = new Mat(), sobel = new Mat(), laplacian = new Mat();

                    Cv2.Canny(gray, canny, this.CannyThreshold1, this.CannyThreshold2);

                    Mat sobelX = new Mat(), sobelY = new Mat();
                    Cv2.Sobel(gray, sobelX, MatType.CV_16S, 1, 0, this.SobelKSize);
                    Cv2.Sobel(gray, sobelY, MatType.CV_16S, 0, 1, this.SobelKSize);
                    Cv2.ConvertScaleAbs(sobelX, sobelX);
                    Cv2.ConvertScaleAbs(sobelY, sobelY);
                    Cv2.AddWeighted(sobelX, 0.5, sobelY, 0.5, 0, sobel);

                    Mat lap = new Mat();
                    Cv2.Laplacian(gray, lap, MatType.CV_16S, this.LaplacianKSize);
                    Cv2.ConvertScaleAbs(lap, laplacian);

                    // 组合结果
                    Cv2.BitwiseOr(canny, sobel, result);
                    Cv2.BitwiseOr(result, laplacian, result);

                    // 清理临时对象
                    canny.Dispose(); sobel.Dispose(); laplacian.Dispose();
                    sobelX.Dispose(); sobelY.Dispose(); lap.Dispose();
                    break;
            }

            // 计算边缘像素数
            this.EdgeCount = Cv2.CountNonZero(result);

            if (gray != src)
                gray.Dispose();

            return this.OK(result, $"{this.Method}边缘检测完成，边缘像素: {this.EdgeCount}");
        }
        catch (Exception ex)
        {
            return this.Error(from?.Mat, $"边缘检测失败: {ex.Message}");
        }
    }

    // 多输出支持
    protected override IEnumerable<IVisionResultImage<Mat>> GetResultImages()
    {
        yield return new VisionResultImage<Mat>() { Name = this.Name + " - 边缘图", Image = this.Mat };
        // 可以添加更多输出，如原图叠加边缘等
    }
}

// 辅助类：核大小数据源
public class KernelSizeItemsSource : IItemsSource
{
    public IEnumerable GetItems(Type type)
    {
        return new int[] { 1, 3, 5, 7 };
    }
}
```

### 案例3：智能ROI提取节点

基于轮廓检测的智能感兴趣区域提取：

```csharp
[Icon(FontIcons.CropFree)]
[Display(Name = "智能ROI提取", GroupName = "区域处理", Description = "基于轮廓检测自动提取感兴趣区域", Order = 3)]
public class SmartROIExtractorNodeData : OpenCVNodeDataBase, IPreprocessingGroupableNodeData
{
    public enum ROISelectionMode
    {
        [Display(Name = "最大轮廓")] LargestContour,
        [Display(Name = "面积阈值")] AreaThreshold,
        [Display(Name = "多个ROI")] MultipleROI,
        [Display(Name = "矩形拟合")] BoundingRect
    }

    public override void LoadDefault()
    {
        base.LoadDefault();
        this.SelectionMode = ROISelectionMode.LargestContour;
        this.MinArea = 1000;
        this.MaxArea = 50000;
        this.ApproximationAccuracy = 2.0;
        this.UseGaussianBlur = true;
        this.BlurKernelSize = new System.Windows.Size(5, 5);
    }

    private ROISelectionMode _selectionMode = ROISelectionMode.LargestContour;
    [Display(Name = "选择模式", GroupName = VisionPropertyGroupNames.RunParameters)]
    public ROISelectionMode SelectionMode
    {
        get { return _selectionMode; }
        set { _selectionMode = value; RaisePropertyChanged(); this.UpdateInvokeCurrent(); }
    }

    private double _minArea = 1000;
    [PropertyItem(typeof(DoubleSliderTextPropertyItem))]
    [Range(100, 100000)]
    [Display(Name = "最小面积", GroupName = VisionPropertyGroupNames.RunParameters)]
    public double MinArea
    {
        get { return _minArea; }
        set { _minArea = value; RaisePropertyChanged(); this.UpdateInvokeCurrent(); }
    }

    private double _maxArea = 50000;
    [PropertyItem(typeof(DoubleSliderTextPropertyItem))]
    [Range(1000, 500000)]
    [Display(Name = "最大面积", GroupName = VisionPropertyGroupNames.RunParameters)]
    public double MaxArea
    {
        get { return _maxArea; }
        set { _maxArea = value; RaisePropertyChanged(); this.UpdateInvokeCurrent(); }
    }

    private double _approximationAccuracy = 2.0;
    [PropertyItem(typeof(DoubleSliderTextPropertyItem))]
    [Range(0.1, 10.0)]
    [Display(Name = "轮廓近似精度", GroupName = VisionPropertyGroupNames.RunParameters)]
    public double ApproximationAccuracy
    {
        get { return _approximationAccuracy; }
        set { _approximationAccuracy = value; RaisePropertyChanged(); this.UpdateInvokeCurrent(); }
    }

    private bool _useGaussianBlur = true;
    [Display(Name = "使用高斯模糊", GroupName = VisionPropertyGroupNames.RunParameters)]
    public bool UseGaussianBlur
    {
        get { return _useGaussianBlur; }
        set { _useGaussianBlur = value; RaisePropertyChanged(); this.UpdateInvokeCurrent(); }
    }

    private System.Windows.Size _blurKernelSize = new System.Windows.Size(5, 5);
    [Display(Name = "模糊核大小", GroupName = VisionPropertyGroupNames.RunParameters)]
    public System.Windows.Size BlurKernelSize
    {
        get { return _blurKernelSize; }
        set { _blurKernelSize = value; RaisePropertyChanged(); this.UpdateInvokeCurrent(); }
    }

    private int _detectedROICount;
    [ReadOnly(true)]
    [Display(Name = "检测到的ROI数量", GroupName = VisionPropertyGroupNames.ResultParameters)]
    public int DetectedROICount
    {
        get { return _detectedROICount; }
        set { _detectedROICount = value; RaisePropertyChanged(); }
    }

    private double _totalArea;
    [ReadOnly(true)]
    [Display(Name = "总面积", GroupName = VisionPropertyGroupNames.ResultParameters)]
    public double TotalArea
    {
        get { return _totalArea; }
        set { _totalArea = value; RaisePropertyChanged(); }
    }

    // 存储检测到的轮廓信息
    private List<OpenCvSharp.Point[]> _detectedContours = new List<OpenCvSharp.Point[]>();
    private List<Rect> _boundingRects = new List<Rect>();

    protected override FlowableResult<Mat> Invoke(ISrcVisionNodeData<Mat> srcImageNodeData, IVisionNodeData<Mat> from, IFlowableDiagramData diagram)
    {
        try
        {
            Mat src = from.Mat;
            if (src?.Empty() != false)
                return this.Error(null, "输入图像为空");

            // 1. 预处理
            Mat gray = src.Channels() == 1 ? src.Clone() : src.CvtColor(ColorConversionCodes.BGR2GRAY);

            if (this.UseGaussianBlur)
            {
                Mat blurred = new Mat();
                Cv2.GaussianBlur(gray, blurred, new OpenCvSharp.Size(this.BlurKernelSize.Width, this.BlurKernelSize.Height), 0);
                gray.Dispose();
                gray = blurred;
            }

            // 2. 二值化
            Mat binary = new Mat();
            Cv2.Threshold(gray, binary, 0, 255, ThresholdTypes.Binary | ThresholdTypes.Otsu);

            // 3. 轮廓检测
            OpenCvSharp.Point[][] contours;
            HierarchyIndex[] hierarchy;
            Cv2.FindContours(binary, out contours, out hierarchy, RetrievalModes.External, ContourApproximationModes.ApproxSimple);

            // 4. 轮廓筛选和处理
            _detectedContours.Clear();
            _boundingRects.Clear();
            double totalArea = 0;

            foreach (var contour in contours)
            {
                double area = Cv2.ContourArea(contour);
                if (area >= this.MinArea && area <= this.MaxArea)
                {
                    // 轮廓近似
                    var approxContour = Cv2.ApproxPolyDP(contour, this.ApproximationAccuracy, true);
                    _detectedContours.Add(approxContour);

                    // 计算边界矩形
                    Rect boundingRect = Cv2.BoundingRect(approxContour);
                    _boundingRects.Add(boundingRect);

                    totalArea += area;
                }
            }

            // 5. 根据选择模式处理结果
            Mat result = src.Clone();

            switch (this.SelectionMode)
            {
                case ROISelectionMode.LargestContour:
                    if (_detectedContours.Count > 0)
                    {
                        var largestContour = _detectedContours
                            .OrderByDescending(c => Cv2.ContourArea(c))
                            .First();

                        // 绘制最大轮廓
                        Cv2.DrawContours(result, new[] { largestContour }, -1, Scalar.Red, 2);

                        // 提取ROI
                        Rect roi = Cv2.BoundingRect(largestContour);
                        result = result[roi];
                    }
                    break;

                case ROISelectionMode.AreaThreshold:
                    // 绘制所有符合面积条件的轮廓
                    for (int i = 0; i < _detectedContours.Count; i++)
                    {
                        Cv2.DrawContours(result, new[] { _detectedContours[i] }, -1, Scalar.Green, 2);
                        Cv2.Rectangle(result, _boundingRects[i], Scalar.Blue, 1);
                    }
                    break;

                case ROISelectionMode.MultipleROI:
                    // 创建掩码，包含所有ROI
                    Mat mask = Mat.Zeros(src.Size(), MatType.CV_8UC1);
                    Cv2.DrawContours(mask, _detectedContours.ToArray(), -1, Scalar.White, -1);

                    // 应用掩码
                    Mat maskedResult = new Mat();
                    src.CopyTo(maskedResult, mask);
                    result = maskedResult;
                    mask.Dispose();
                    break;

                case ROISelectionMode.BoundingRect:
                    // 绘制边界矩形
                    foreach (var rect in _boundingRects)
                    {
                        Cv2.Rectangle(result, rect, Scalar.Yellow, 2);
                    }
                    break;
            }

            // 6. 更新结果参数
            this.DetectedROICount = _detectedContours.Count;
            this.TotalArea = totalArea;

            // 7. 清理临时对象
            if (gray != src) gray.Dispose();
            binary.Dispose();

            return this.OK(result, $"ROI提取完成，检测到 {this.DetectedROICount} 个区域，总面积: {this.TotalArea:F0}");
        }
        catch (Exception ex)
        {
            return this.Error(from?.Mat, $"ROI提取失败: {ex.Message}");
        }
    }

    // 多输出支持：提供原图、二值图、轮廓图等
    protected override IEnumerable<IVisionResultImage<Mat>> GetResultImages()
    {
        yield return new VisionResultImage<Mat>() { Name = this.Name + " - ROI结果", Image = this.Mat };

        // 可以添加更多输出
        if (_detectedContours.Count > 0)
        {
            // 创建轮廓可视化图
            Mat contourImage = Mat.Zeros(this.Mat.Size(), MatType.CV_8UC3);
            for (int i = 0; i < _detectedContours.Count; i++)
            {
                var color = new Scalar(
                    new Random().Next(0, 255),
                    new Random().Next(0, 255),
                    new Random().Next(0, 255)
                );
                Cv2.DrawContours(contourImage, new[] { _detectedContours[i] }, -1, color, 2);
            }
            yield return new VisionResultImage<Mat>() { Name = this.Name + " - 轮廓图", Image = contourImage };
        }
    }

    // 提供轮廓数据给下游节点使用
    public IReadOnlyList<OpenCvSharp.Point[]> GetDetectedContours() => _detectedContours.AsReadOnly();
    public IReadOnlyList<Rect> GetBoundingRects() => _boundingRects.AsReadOnly();
}
```

## 🎯 案例总结

这三个实际案例展示了不同复杂度的节点开发：

1. **自适应阈值节点** - 展示了参数验证、属性配置和基础图像处理
2. **多算法边缘检测节点** - 展示了枚举选择、多算法组合和结果统计
3. **智能ROI提取节点** - 展示了复杂逻辑、多输出支持和数据共享

每个案例都包含了：
- ✅ 完整的属性配置
- ✅ 参数验证和错误处理
- ✅ 内存管理（Mat对象释放）
- ✅ 结果统计和反馈
- ✅ 多输出支持
- ✅ 实时预览更新

---

*本指南基于 WPF-VisionMaster 项目的实际代码分析生成，涵盖了从基础开发到高级优化的完整流程，并提供了丰富的实际案例，确保开发者能够快速上手并创建高质量的视觉处理节点。*
