{"$type": "System.Collections.ObjectModel.ObservableCollection`1[[H.VisionMaster.DiagramData.IVisionDiagramData, H.VisionMaster.DiagramData]], System.ObjectModel", "$values": [{"$id": "1", "$type": "H.App.VisionMaster.OpenCV.DiagramDatas.OpenCVVisionDiagramData, H.App.VisionMaster.OpenCV", "RunModeResult": "True", "UseFlowableSelectToRunning": true, "Name": "机器视觉流程", "Width": 1000.0, "Height": 1500.0, "Location": "1000,650", "Message": "运行成功", "Datas": {"$type": "H.Controls.Diagram.Presenter.DiagramDatas.Base.Datas, H.Controls.Diagram.Presenter", "NodeDatas": {"$type": "System.Collections.Generic.List`1[[H.Controls.Diagram.Datas.INodeData, H.Controls.Diagram]], System.Private.CoreLib", "$values": [{"$id": "2", "$type": "H.VisionMaster.OpenCV.NodeDatas.Src.SrcImageFilesNodeData, H.VisionMaster.OpenCV", "PixelWidth": 1887, "PixelHeight": 1515, "ImageColorType": 16, "UseAutoSwitch": true, "SrcFilePath": "Assets\\green.jpg", "SrcFilePaths": {"$type": "System.Collections.ObjectModel.ObservableCollection`1[[System.String, System.Private.CoreLib]], System.ObjectModel", "$values": ["Assets\\BaseImages\\a01.png", "Assets\\BaseImages\\alpha1.png", "Assets\\BaseImages\\alpha2.png", "Assets\\BaseImages\\angio-part.png", "Assets\\BaseImages\\atoms.png", "Assets\\BaseImages\\audi2.png", "Assets\\BaseImages\\autobahn.png", "Assets\\BaseImages\\b5_1.png", "Assets\\BaseImages\\bga_14x14_defects.png", "Assets\\BaseImages\\bga_14x14_model.png", "Assets\\BaseImages\\bitrot.tif", "Assets\\BaseImages\\bk45.png", "Assets\\BaseImages\\bottle2.png", "Assets\\BaseImages\\brycecanyon1.png", "Assets\\BaseImages\\cable1.png", "Assets\\BaseImages\\cable2.png", "Assets\\BaseImages\\caltab.png", "Assets\\BaseImages\\can.png", "Assets\\BaseImages\\can_with_grid.png", "Assets\\BaseImages\\circle_plate.png", "Assets\\BaseImages\\circular_barcode.png", "Assets\\BaseImages\\claudia.png", "Assets\\BaseImages\\clip.png", "Assets\\BaseImages\\combine.png", "Assets\\BaseImages\\crystal.png", "Assets\\BaseImages\\die_on_chip.png", "Assets\\BaseImages\\die_pads.png", "Assets\\BaseImages\\dot_print_slanted.png", "Assets\\BaseImages\\double_circle.png", "Assets\\BaseImages\\earth.png", "Assets\\BaseImages\\ed_g.png", "Assets\\BaseImages\\egypt1.png", "Assets\\BaseImages\\engraved.png", "Assets\\BaseImages\\fabrik.png", "Assets\\BaseImages\\fin1.png", "Assets\\BaseImages\\fin2.png", "Assets\\BaseImages\\fin3.png", "Assets\\BaseImages\\fingerprint.png", "Assets\\BaseImages\\for5.png", "Assets\\BaseImages\\for6.png", "Assets\\BaseImages\\forest_air1.png", "Assets\\BaseImages\\forest_road.png", "Assets\\BaseImages\\fuse.png", "Assets\\BaseImages\\glasses_polarized.png", "Assets\\BaseImages\\graph_paper.png", "Assets\\BaseImages\\green-dot.png", "Assets\\BaseImages\\green-dots.png", "Assets\\BaseImages\\horses.png", "Assets\\BaseImages\\hull.png", "Assets\\BaseImages\\ic.png", "Assets\\BaseImages\\ic0.png", "Assets\\BaseImages\\ic1.png", "Assets\\BaseImages\\ic2.png", "Assets\\BaseImages\\ic3.png", "Assets\\BaseImages\\ic_pin.png", "Assets\\BaseImages\\keypad.png", "Assets\\BaseImages\\landmarks.png", "Assets\\BaseImages\\largebw1.tif", "Assets\\BaseImages\\letters.png", "Assets\\BaseImages\\lynx.png", "Assets\\BaseImages\\lynx_mask.png", "Assets\\BaseImages\\marks.png", "Assets\\BaseImages\\meningg5.png", "Assets\\BaseImages\\meningg6.png", "Assets\\BaseImages\\monkey.png", "Assets\\BaseImages\\montery.png", "Assets\\BaseImages\\mreut.png", "Assets\\BaseImages\\mreut4_3.png", "Assets\\BaseImages\\mreut_dgm_2.0.tif", "Assets\\BaseImages\\mreut_hill.png", "Assets\\BaseImages\\mreut_y.png", "Assets\\BaseImages\\multiple_dies_01.png", "Assets\\BaseImages\\multiple_dies_02.png", "Assets\\BaseImages\\mvtec_logo.png", "Assets\\BaseImages\\needle1.png", "Assets\\BaseImages\\numbers_scale.png", "Assets\\BaseImages\\olympic_stadium.png", "Assets\\BaseImages\\pads.png", "Assets\\BaseImages\\particle.png", "Assets\\BaseImages\\patras.png", "Assets\\BaseImages\\pcb.png", "Assets\\BaseImages\\pcb_color.png", "Assets\\BaseImages\\pcb_layout.png", "Assets\\BaseImages\\pellets.png", "Assets\\BaseImages\\pioneer.png", "Assets\\BaseImages\\plan_01.png", "Assets\\BaseImages\\plan_02.png", "Assets\\BaseImages\\plit2.png", "Assets\\BaseImages\\progres.png", "Assets\\BaseImages\\pumpe.png", "Assets\\BaseImages\\punched_holes.png", "Assets\\BaseImages\\razors1.png", "Assets\\BaseImages\\razors2.png", "Assets\\BaseImages\\rim.png", "Assets\\BaseImages\\rings_and_nuts.png", "Assets\\BaseImages\\screw_thread.png", "Assets\\BaseImages\\surface_scratch.png", "Assets\\BaseImages\\tooth_rim.png", "Assets\\BaseImages\\traffic1.png", "Assets\\BaseImages\\traffic2.png", "Assets\\BaseImages\\vessel.png", "Assets\\BaseImages\\woodring.png", "Assets\\BaseImages\\wood_knots.png", "Assets\\BaseImages\\zeiss1.png", "Assets\\board\\board-01.png", "Assets\\board\\board-02.png", "Assets\\board\\board-03.png", "Assets\\board\\board-04.png", "Assets\\board\\board-05.png", "Assets\\board\\board-06.png", "Assets\\board\\board-07.png", "Assets\\board\\board-08.png", "Assets\\board\\board-09.png", "Assets\\board\\board-10.png", "Assets\\board\\board-11.png", "Assets\\board\\board-12.png", "Assets\\board\\board-13.png", "Assets\\board\\board-14.png", "Assets\\board\\board-15.png", "Assets\\board\\board-16.png", "Assets\\board\\board-17.png", "Assets\\board\\board-18.png", "Assets\\board\\board-19.png", "Assets\\board\\board-20.png", "Assets\\board\\board_01.png", "Assets\\board\\board_02.png", "Assets\\board\\board_03.png", "Assets\\board\\board_04.png", "Assets\\board\\board_05.png", "Assets\\board\\board_06.png", "Assets\\board\\board_07.png", "Assets\\board\\board_08.png", "Assets\\board\\board_09.png", "Assets\\bottle_crate\\bottle_crate_01.png", "Assets\\bottle_crate\\bottle_crate_02.png", "Assets\\bottle_crate\\bottle_crate_03.png", "Assets\\bottle_crate\\bottle_crate_04.png", "Assets\\bottle_crate\\bottle_crate_05.png", "Assets\\bottle_crate\\bottle_crate_06.png", "Assets\\bottle_crate\\bottle_crate_07.png", "Assets\\bottle_crate\\bottle_crate_08.png", "Assets\\bottle_crate\\bottle_crate_09.png", "Assets\\bottle_crate\\bottle_crate_10.png", "Assets\\bottle_crate\\bottle_crate_11.png", "Assets\\bottle_crate\\bottle_crate_12.png", "Assets\\bottle_crate\\bottle_crate_13.png", "Assets\\bottle_crate\\bottle_crate_14.png", "Assets\\bottle_crate\\bottle_crate_15.png", "Assets\\bottle_crate\\bottle_crate_16.png", "Assets\\bottle_crate\\bottle_crate_17.png", "Assets\\bottle_crate\\bottle_crate_18.png", "Assets\\bottle_crate\\bottle_crate_19.png", "Assets\\bottle_crate\\bottle_crate_20.png", "Assets\\bottle_crate\\bottle_crate_21.png", "Assets\\bottle_crate\\bottle_crate_22.png", "Assets\\bottle_crate\\bottle_crate_23.png", "Assets\\bottle_crate\\bottle_crate_24.png", "Assets\\box.bmp", "Assets\\car_door\\car_door_01.png", "Assets\\car_door\\car_door_02.png", "Assets\\car_door\\car_door_03.png", "Assets\\car_door\\car_door_04.png", "Assets\\car_door\\car_door_05.png", "Assets\\car_door\\car_door_06.png", "Assets\\car_door\\car_door_07.png", "Assets\\car_door\\car_door_08.png", "Assets\\car_door\\car_door_09.png", "Assets\\car_door\\car_door_10.png", "Assets\\car_door\\car_door_11.png", "Assets\\car_door\\car_door_12.png", "Assets\\car_door\\car_door_13.png", "Assets\\car_door\\car_door_14.png", "Assets\\car_door\\car_door_15.png", "Assets\\car_door\\car_door_16.png", "Assets\\car_door\\car_door_17.png", "Assets\\car_door\\car_door_18.png", "Assets\\car_door\\car_door_19.png", "Assets\\car_door\\car_door_20.png", "Assets\\car_door\\car_door_21.png", "Assets\\car_door\\car_door_calib_plate.png", "Assets\\car_door\\car_door_init_pose.png", "Assets\\green.jpg", "Assets\\multi_view_bitholder_cam\\multi_view_bitholder_cam_0_1.png", "Assets\\multi_view_bitholder_cam\\multi_view_bitholder_cam_0_10.png", "Assets\\multi_view_bitholder_cam\\multi_view_bitholder_cam_0_2.png", "Assets\\multi_view_bitholder_cam\\multi_view_bitholder_cam_0_3.png", "Assets\\multi_view_bitholder_cam\\multi_view_bitholder_cam_0_4.png", "Assets\\multi_view_bitholder_cam\\multi_view_bitholder_cam_0_7.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_01.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_02.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_03.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_04.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_05.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_06.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_07.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_08.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_09.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_10.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_11.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_12.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_13.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_14.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_15.png", "Assets\\OpenCV\\00.JPG", "Assets\\OpenCV\\01.JPG", "Assets\\OpenCV\\02.JPG", "Assets\\OpenCV\\16bit.png", "Assets\\OpenCV\\aruco_markers_photo.jpg", "Assets\\OpenCV\\aruco_markers_source.jpg", "Assets\\OpenCV\\asahiyama.jpg", "Assets\\OpenCV\\Balloon.png", "Assets\\OpenCV\\binarization_sample.bmp", "Assets\\OpenCV\\box.png", "Assets\\OpenCV\\box_in_scene.png", "Assets\\OpenCV\\cake.bmp", "Assets\\OpenCV\\cat.jpg", "Assets\\OpenCV\\circle.png", "Assets\\OpenCV\\cvmorph.png", "Assets\\OpenCV\\fruits.jpg", "Assets\\OpenCV\\Girl.bmp", "Assets\\OpenCV\\goryokaku.jpg", "Assets\\OpenCV\\hand_p.jpg", "Assets\\OpenCV\\left01.jpg", "Assets\\OpenCV\\left02.jpg", "Assets\\OpenCV\\left03.jpg", "Assets\\OpenCV\\left04.jpg", "Assets\\OpenCV\\left05.jpg", "Assets\\OpenCV\\left06.jpg", "Assets\\OpenCV\\left07.jpg", "Assets\\OpenCV\\left08.jpg", "Assets\\OpenCV\\left09.jpg", "Assets\\OpenCV\\left10.jpg", "Assets\\OpenCV\\left11.jpg", "Assets\\OpenCV\\left12.jpg", "Assets\\OpenCV\\left13.jpg", "Assets\\OpenCV\\lenna.png", "Assets\\OpenCV\\lenna511.png", "Assets\\OpenCV\\maltese.jpg", "Assets\\OpenCV\\Mandrill.bmp", "Assets\\OpenCV\\match1.png", "Assets\\OpenCV\\match2.png", "Assets\\OpenCV\\penguin1.png", "Assets\\OpenCV\\penguin1b.png", "Assets\\OpenCV\\penguin2.png", "Assets\\OpenCV\\pentagon.png", "Assets\\OpenCV\\pic1.png", "Assets\\OpenCV\\pic2.png", "Assets\\OpenCV\\pic3.png", "Assets\\OpenCV\\pic4.png", "Assets\\OpenCV\\pic5.png", "Assets\\OpenCV\\pic6.png", "Assets\\OpenCV\\right01.jpg", "Assets\\OpenCV\\right02.jpg", "Assets\\OpenCV\\right03.jpg", "Assets\\OpenCV\\right04.jpg", "Assets\\OpenCV\\right05.jpg", "Assets\\OpenCV\\right06.jpg", "Assets\\OpenCV\\right07.jpg", "Assets\\OpenCV\\right08.jpg", "Assets\\OpenCV\\right09.jpg", "Assets\\OpenCV\\right10.jpg", "Assets\\OpenCV\\right11.jpg", "Assets\\OpenCV\\right12.jpg", "Assets\\OpenCV\\right13.jpg", "Assets\\OpenCV\\shapes.png", "Assets\\OpenCV\\space_shuttle.jpg", "Assets\\OpenCV\\tsukuba_left.png", "Assets\\OpenCV\\tsukuba_right.png", "Assets\\OpenCV\\very_old_newspaper.png", "Assets\\OpenCV\\walkman.jpg", "Assets\\OpenCV\\yalta.jpg", "Assets\\Person\\009445.jpg", "Assets\\Person\\009453.jpg", "Assets\\Person\\009456.jpg", "Assets\\Person\\1.jpg", "Assets\\Person\\Image1.jpg", "Assets\\Person\\Image4.jpg", "Assets\\Person\\Image5.jpg", "Assets\\Person\\woman.jpg", "Assets\\Person\\yolov6-inference-cycling.jpg", "Assets\\Person\\yolov6-inference-soccer.jpg", "Assets\\pill_bag\\pill_bag_001.png", "Assets\\pill_bag\\pill_bag_002.png", "Assets\\pill_bag\\pill_bag_003.png", "Assets\\pill_bag\\pill_bag_004.png", "Assets\\pill_bag\\pill_bag_005.png", "Assets\\pill_bag\\pill_bag_006.png", "Assets\\pill_bag\\pill_bag_007.png", "Assets\\pill_bag\\pill_bag_008.png", "Assets\\pill_bag\\pill_bag_009.png", "Assets\\pill_bag\\pill_bag_010.png", "Assets\\pill_magnesium_crack\\pill_magnesium_crack_280.png", "Assets\\pill_magnesium_crack\\pill_magnesium_crack_281.png", "Assets\\pill_magnesium_crack\\pill_magnesium_crack_282.png", "Assets\\pill_magnesium_crack\\pill_magnesium_crack_283.png", "Assets\\pill_magnesium_crack\\pill_magnesium_crack_284.png", "Assets\\pill_magnesium_crack\\pill_magnesium_crack_285.png", "Assets\\pill_magnesium_crack\\pill_magnesium_crack_286.png", "Assets\\pill_magnesium_crack\\pill_magnesium_crack_287.png", "Assets\\pill_magnesium_crack\\pill_magnesium_crack_288.png", "Assets\\pill_magnesium_crack\\pill_magnesium_crack_289.png", "Assets\\radius-gauges\\radius-gauges-00.png", "Assets\\radius-gauges\\radius-gauges-01.png", "Assets\\radius-gauges\\radius-gauges-02.png", "Assets\\radius-gauges\\radius-gauges-03.png", "Assets\\radius-gauges\\radius-gauges-04.png", "Assets\\radius-gauges\\radius-gauges-05.png", "Assets\\radius-gauges\\radius-gauges-06.png", "Assets\\radius-gauges\\radius-gauges-07.png", "Assets\\radius-gauges\\radius-gauges-08.png", "Assets\\radius-gauges\\radius-gauges-09.png", "Assets\\radius-gauges\\radius-gauges-10.png", "Assets\\radius-gauges\\radius-gauges-11.png", "Assets\\radius-gauges\\radius-gauges-12.png", "Assets\\radius-gauges\\radius-gauges-13.png", "Assets\\radius-gauges\\radius-gauges-14.png", "Assets\\radius-gauges\\radius-gauges-15.png", "Assets\\radius-gauges\\radius-gauges-16.png", "Assets\\radius-gauges\\radius-gauges-17.png", "Assets\\radius-gauges\\radius-gauges-18.png", "Assets\\radius-gauges\\radius-gauges-19.png", "Assets\\radius-gauges\\radius-gauges-20.png", "Assets\\rice.png", "Assets\\zhifang_ball.png"]}, "ROI": {"$id": "3", "$type": "H.VisionMaster.NodeData.ROIPresenters.FromROI, H.VisionMaster.NodeData", "ID": "7dd1e8f6-30b2-431c-953c-a8bb6a0ca349", "Name": "继承"}, "FromROI": {"$ref": "3"}, "DrawROI": {"$id": "4", "$type": "H.VisionMaster.NodeData.ROIPresenters.DrawROI, H.VisionMaster.NodeData", "Rect": "Empty", "ID": "c8dbf9e2-64ed-40bd-ad10-5def157cff3c", "Name": "绘制"}, "InputROI": {"$id": "5", "$type": "H.VisionMaster.NodeData.ROIPresenters.InputROI, H.VisionMaster.NodeData", "ID": "bb826b65-0cd7-499c-8764-8181866b5c18", "Name": "输入"}, "PreviewMillisecondsDelay": 0, "InvokeMillisecondsDelay": 0, "FlagLength": 10.0, "State": "Success", "TimeSpan": "00:00:00.8132118", "Message": "运行成功", "DiagramData": {"$ref": "1"}, "Text": "本地图像源", "FontSize": 12.0, "PortDatas": {"$type": "System.Collections.Generic.List`1[[H.Controls.Diagram.Datas.IPortData, H.Controls.Diagram]], System.Private.CoreLib", "$values": [{"$id": "6", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "State": "Wait", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Top", "NodeID": "7290db37-a490-4cc1-848f-4604f68a1566", "PortType": "Input", "ID": "961bc524-2b29-42e5-ad71-be3f86946101"}, {"$id": "7", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "State": "Wait", "Message": "运行成功", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Bottom", "NodeID": "7290db37-a490-4cc1-848f-4604f68a1566", "PortType": "OutPut", "ID": "b66c4bd6-958e-47e9-ae28-98950f542446"}, {"$id": "8", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "State": "Wait", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "NodeID": "7290db37-a490-4cc1-848f-4604f68a1566", "PortType": "Input", "ID": "57e5530a-3dbe-4aa1-a027-9eb39b93cdfe"}, {"$id": "9", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "State": "Wait", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Right", "NodeID": "7290db37-a490-4cc1-848f-4604f68a1566", "PortType": "OutPut", "ID": "be847c58-7e64-4f04-86bc-0d620ed9387b"}]}, "IsTemplate": false, "Height": 35.0, "Width": 120.0, "Fill": "#FFFFFFFF", "CornerRadius": 2.0, "Location": "487.0074074074074,538.5555555555554", "ID": "7290db37-a490-4cc1-848f-4604f68a1566", "Name": "本地图像源", "Icon": ""}, {"$id": "10", "$type": "H.VisionMaster.OpenCV.NodeDatas.Basic.HSVInRange, H.VisionMaster.OpenCV", "ImageColorPickerPresenter": {"$id": "11", "$type": "H.VisionMaster.NodeData.ImageColorPickerPresenter, H.VisionMaster.NodeData", "Color": "#FF8BDC27"}, "hRange": 39, "sRange": 32, "vRange": 58, "ROI": {"$id": "12", "$type": "H.VisionMaster.NodeData.ROIPresenters.FromROI, H.VisionMaster.NodeData", "ID": "9f825a8d-fe5e-4f74-ac1f-87293f42e06a", "Name": "继承"}, "FromROI": {"$ref": "12"}, "DrawROI": {"$id": "13", "$type": "H.VisionMaster.NodeData.ROIPresenters.DrawROI, H.VisionMaster.NodeData", "Rect": "Empty", "ID": "2b7e0dd7-5e68-43ed-9cc2-a6c65a177120", "Name": "绘制"}, "InputROI": {"$id": "14", "$type": "H.VisionMaster.NodeData.ROIPresenters.InputROI, H.VisionMaster.NodeData", "ID": "466725af-fa5e-484c-aa66-f830cfceffc3", "Name": "输入"}, "PreviewMillisecondsDelay": 0, "InvokeMillisecondsDelay": 0, "FlagLength": 10.0, "State": "Success", "TimeSpan": "00:00:00.0471381", "Message": "运行成功", "DiagramData": {"$ref": "1"}, "Text": "HSV二值分割", "FontSize": 12.0, "PortDatas": {"$type": "System.Collections.Generic.List`1[[H.Controls.Diagram.Datas.IPortData, H.Controls.Diagram]], System.Private.CoreLib", "$values": [{"$id": "15", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "State": "Wait", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Top", "NodeID": "9d9743e0-f47f-4fac-b978-f293a3bb2ae4", "PortType": "Input", "ID": "9256f752-e6a8-42d4-9d8b-07e8fd97daf0"}, {"$id": "16", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "State": "Wait", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Bottom", "NodeID": "9d9743e0-f47f-4fac-b978-f293a3bb2ae4", "PortType": "OutPut", "ID": "495d0582-48eb-4894-9a71-e81280abd1d3"}, {"$id": "17", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "State": "Wait", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "NodeID": "9d9743e0-f47f-4fac-b978-f293a3bb2ae4", "PortType": "Input", "ID": "09ac0a88-605c-4c19-8e14-009b46bcf786"}, {"$id": "18", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "State": "Wait", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Right", "NodeID": "9d9743e0-f47f-4fac-b978-f293a3bb2ae4", "PortType": "OutPut", "ID": "6c798b91-a03b-4780-b79c-50a23a3965ab"}]}, "IsTemplate": false, "Height": 35.0, "Width": 120.0, "Fill": "#FFFFFFFF", "CornerRadius": 2.0, "Location": "487.0074074074074,626.8148148148146", "ID": "9d9743e0-f47f-4fac-b978-f293a3bb2ae4", "Name": "HSV二值分割", "Icon": ""}]}, "LinkDatas": {"$type": "System.Collections.Generic.List`1[[H.Controls.Diagram.Datas.ILinkData, H.Controls.Diagram]], System.Private.CoreLib", "$values": [{"$id": "19", "$type": "H.Controls.Diagram.Presenter.LinkDatas.FlowableLinkData, H.Controls.Diagram.Presenter", "State": "Wait", "FontSize": 12.0, "Stroke": "#FFFFA500", "FromNodeID": "7290db37-a490-4cc1-848f-4604f68a1566", "ToNodeID": "9d9743e0-f47f-4fac-b978-f293a3bb2ae4", "FromPortID": "b66c4bd6-958e-47e9-ae28-98950f542446", "ToPortID": "9256f752-e6a8-42d4-9d8b-07e8fd97daf0", "ID": "fa0ae2db-22f1-4e3b-8cea-7a34146327d1", "Name": "连线"}]}}, "ID": "16af6e43-e1cc-4d12-b52b-d0be059038b2"}, {"$id": "20", "$type": "H.App.VisionMaster.OpenCV.DiagramDatas.OpenCVVisionDiagramData, H.App.VisionMaster.OpenCV", "RunModeResult": "True", "UseFlowableSelectToRunning": true, "Name": "机器视觉流程", "Width": 1000.0, "Height": 1500.0, "Location": "1000,650", "Message": "运行成功", "Datas": {"$type": "H.Controls.Diagram.Presenter.DiagramDatas.Base.Datas, H.Controls.Diagram.Presenter", "NodeDatas": {"$type": "System.Collections.Generic.List`1[[H.Controls.Diagram.Datas.INodeData, H.Controls.Diagram]], System.Private.CoreLib", "$values": [{"$id": "21", "$type": "H.VisionMaster.OpenCV.NodeDatas.Src.SrcImageFilesNodeData, H.VisionMaster.OpenCV", "PixelWidth": 367, "PixelHeight": 350, "ImageColorType": 16, "UseAutoSwitch": true, "SrcFilePath": "Assets\\Person\\woman.jpg", "SrcFilePaths": {"$type": "System.Collections.ObjectModel.ObservableCollection`1[[System.String, System.Private.CoreLib]], System.ObjectModel", "$values": ["Assets\\BaseImages\\a01.png", "Assets\\BaseImages\\alpha1.png", "Assets\\BaseImages\\alpha2.png", "Assets\\BaseImages\\angio-part.png", "Assets\\BaseImages\\atoms.png", "Assets\\BaseImages\\audi2.png", "Assets\\BaseImages\\autobahn.png", "Assets\\BaseImages\\b5_1.png", "Assets\\BaseImages\\bga_14x14_defects.png", "Assets\\BaseImages\\bga_14x14_model.png", "Assets\\BaseImages\\bitrot.tif", "Assets\\BaseImages\\bk45.png", "Assets\\BaseImages\\bottle2.png", "Assets\\BaseImages\\brycecanyon1.png", "Assets\\BaseImages\\cable1.png", "Assets\\BaseImages\\cable2.png", "Assets\\BaseImages\\caltab.png", "Assets\\BaseImages\\can.png", "Assets\\BaseImages\\can_with_grid.png", "Assets\\BaseImages\\circle_plate.png", "Assets\\BaseImages\\circular_barcode.png", "Assets\\BaseImages\\claudia.png", "Assets\\BaseImages\\clip.png", "Assets\\BaseImages\\combine.png", "Assets\\BaseImages\\crystal.png", "Assets\\BaseImages\\die_on_chip.png", "Assets\\BaseImages\\die_pads.png", "Assets\\BaseImages\\dot_print_slanted.png", "Assets\\BaseImages\\double_circle.png", "Assets\\BaseImages\\earth.png", "Assets\\BaseImages\\ed_g.png", "Assets\\BaseImages\\egypt1.png", "Assets\\BaseImages\\engraved.png", "Assets\\BaseImages\\fabrik.png", "Assets\\BaseImages\\fin1.png", "Assets\\BaseImages\\fin2.png", "Assets\\BaseImages\\fin3.png", "Assets\\BaseImages\\fingerprint.png", "Assets\\BaseImages\\for5.png", "Assets\\BaseImages\\for6.png", "Assets\\BaseImages\\forest_air1.png", "Assets\\BaseImages\\forest_road.png", "Assets\\BaseImages\\fuse.png", "Assets\\BaseImages\\glasses_polarized.png", "Assets\\BaseImages\\graph_paper.png", "Assets\\BaseImages\\green-dot.png", "Assets\\BaseImages\\green-dots.png", "Assets\\BaseImages\\horses.png", "Assets\\BaseImages\\hull.png", "Assets\\BaseImages\\ic.png", "Assets\\BaseImages\\ic0.png", "Assets\\BaseImages\\ic1.png", "Assets\\BaseImages\\ic2.png", "Assets\\BaseImages\\ic3.png", "Assets\\BaseImages\\ic_pin.png", "Assets\\BaseImages\\keypad.png", "Assets\\BaseImages\\landmarks.png", "Assets\\BaseImages\\largebw1.tif", "Assets\\BaseImages\\letters.png", "Assets\\BaseImages\\lynx.png", "Assets\\BaseImages\\lynx_mask.png", "Assets\\BaseImages\\marks.png", "Assets\\BaseImages\\meningg5.png", "Assets\\BaseImages\\meningg6.png", "Assets\\BaseImages\\monkey.png", "Assets\\BaseImages\\montery.png", "Assets\\BaseImages\\mreut.png", "Assets\\BaseImages\\mreut4_3.png", "Assets\\BaseImages\\mreut_dgm_2.0.tif", "Assets\\BaseImages\\mreut_hill.png", "Assets\\BaseImages\\mreut_y.png", "Assets\\BaseImages\\multiple_dies_01.png", "Assets\\BaseImages\\multiple_dies_02.png", "Assets\\BaseImages\\mvtec_logo.png", "Assets\\BaseImages\\needle1.png", "Assets\\BaseImages\\numbers_scale.png", "Assets\\BaseImages\\olympic_stadium.png", "Assets\\BaseImages\\pads.png", "Assets\\BaseImages\\particle.png", "Assets\\BaseImages\\patras.png", "Assets\\BaseImages\\pcb.png", "Assets\\BaseImages\\pcb_color.png", "Assets\\BaseImages\\pcb_layout.png", "Assets\\BaseImages\\pellets.png", "Assets\\BaseImages\\pioneer.png", "Assets\\BaseImages\\plan_01.png", "Assets\\BaseImages\\plan_02.png", "Assets\\BaseImages\\plit2.png", "Assets\\BaseImages\\progres.png", "Assets\\BaseImages\\pumpe.png", "Assets\\BaseImages\\punched_holes.png", "Assets\\BaseImages\\razors1.png", "Assets\\BaseImages\\razors2.png", "Assets\\BaseImages\\rim.png", "Assets\\BaseImages\\rings_and_nuts.png", "Assets\\BaseImages\\screw_thread.png", "Assets\\BaseImages\\surface_scratch.png", "Assets\\BaseImages\\tooth_rim.png", "Assets\\BaseImages\\traffic1.png", "Assets\\BaseImages\\traffic2.png", "Assets\\BaseImages\\vessel.png", "Assets\\BaseImages\\woodring.png", "Assets\\BaseImages\\wood_knots.png", "Assets\\BaseImages\\zeiss1.png", "Assets\\board\\board-01.png", "Assets\\board\\board-02.png", "Assets\\board\\board-03.png", "Assets\\board\\board-04.png", "Assets\\board\\board-05.png", "Assets\\board\\board-06.png", "Assets\\board\\board-07.png", "Assets\\board\\board-08.png", "Assets\\board\\board-09.png", "Assets\\board\\board-10.png", "Assets\\board\\board-11.png", "Assets\\board\\board-12.png", "Assets\\board\\board-13.png", "Assets\\board\\board-14.png", "Assets\\board\\board-15.png", "Assets\\board\\board-16.png", "Assets\\board\\board-17.png", "Assets\\board\\board-18.png", "Assets\\board\\board-19.png", "Assets\\board\\board-20.png", "Assets\\board\\board_01.png", "Assets\\board\\board_02.png", "Assets\\board\\board_03.png", "Assets\\board\\board_04.png", "Assets\\board\\board_05.png", "Assets\\board\\board_06.png", "Assets\\board\\board_07.png", "Assets\\board\\board_08.png", "Assets\\board\\board_09.png", "Assets\\bottle_crate\\bottle_crate_01.png", "Assets\\bottle_crate\\bottle_crate_02.png", "Assets\\bottle_crate\\bottle_crate_03.png", "Assets\\bottle_crate\\bottle_crate_04.png", "Assets\\bottle_crate\\bottle_crate_05.png", "Assets\\bottle_crate\\bottle_crate_06.png", "Assets\\bottle_crate\\bottle_crate_07.png", "Assets\\bottle_crate\\bottle_crate_08.png", "Assets\\bottle_crate\\bottle_crate_09.png", "Assets\\bottle_crate\\bottle_crate_10.png", "Assets\\bottle_crate\\bottle_crate_11.png", "Assets\\bottle_crate\\bottle_crate_12.png", "Assets\\bottle_crate\\bottle_crate_13.png", "Assets\\bottle_crate\\bottle_crate_14.png", "Assets\\bottle_crate\\bottle_crate_15.png", "Assets\\bottle_crate\\bottle_crate_16.png", "Assets\\bottle_crate\\bottle_crate_17.png", "Assets\\bottle_crate\\bottle_crate_18.png", "Assets\\bottle_crate\\bottle_crate_19.png", "Assets\\bottle_crate\\bottle_crate_20.png", "Assets\\bottle_crate\\bottle_crate_21.png", "Assets\\bottle_crate\\bottle_crate_22.png", "Assets\\bottle_crate\\bottle_crate_23.png", "Assets\\bottle_crate\\bottle_crate_24.png", "Assets\\box.bmp", "Assets\\car_door\\car_door_01.png", "Assets\\car_door\\car_door_02.png", "Assets\\car_door\\car_door_03.png", "Assets\\car_door\\car_door_04.png", "Assets\\car_door\\car_door_05.png", "Assets\\car_door\\car_door_06.png", "Assets\\car_door\\car_door_07.png", "Assets\\car_door\\car_door_08.png", "Assets\\car_door\\car_door_09.png", "Assets\\car_door\\car_door_10.png", "Assets\\car_door\\car_door_11.png", "Assets\\car_door\\car_door_12.png", "Assets\\car_door\\car_door_13.png", "Assets\\car_door\\car_door_14.png", "Assets\\car_door\\car_door_15.png", "Assets\\car_door\\car_door_16.png", "Assets\\car_door\\car_door_17.png", "Assets\\car_door\\car_door_18.png", "Assets\\car_door\\car_door_19.png", "Assets\\car_door\\car_door_20.png", "Assets\\car_door\\car_door_21.png", "Assets\\car_door\\car_door_calib_plate.png", "Assets\\car_door\\car_door_init_pose.png", "Assets\\green.jpg", "Assets\\multi_view_bitholder_cam\\multi_view_bitholder_cam_0_1.png", "Assets\\multi_view_bitholder_cam\\multi_view_bitholder_cam_0_10.png", "Assets\\multi_view_bitholder_cam\\multi_view_bitholder_cam_0_2.png", "Assets\\multi_view_bitholder_cam\\multi_view_bitholder_cam_0_3.png", "Assets\\multi_view_bitholder_cam\\multi_view_bitholder_cam_0_4.png", "Assets\\multi_view_bitholder_cam\\multi_view_bitholder_cam_0_7.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_01.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_02.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_03.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_04.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_05.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_06.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_07.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_08.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_09.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_10.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_11.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_12.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_13.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_14.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_15.png", "Assets\\OpenCV\\00.JPG", "Assets\\OpenCV\\01.JPG", "Assets\\OpenCV\\02.JPG", "Assets\\OpenCV\\16bit.png", "Assets\\OpenCV\\aruco_markers_photo.jpg", "Assets\\OpenCV\\aruco_markers_source.jpg", "Assets\\OpenCV\\asahiyama.jpg", "Assets\\OpenCV\\Balloon.png", "Assets\\OpenCV\\binarization_sample.bmp", "Assets\\OpenCV\\box.png", "Assets\\OpenCV\\box_in_scene.png", "Assets\\OpenCV\\cake.bmp", "Assets\\OpenCV\\cat.jpg", "Assets\\OpenCV\\circle.png", "Assets\\OpenCV\\cvmorph.png", "Assets\\OpenCV\\fruits.jpg", "Assets\\OpenCV\\Girl.bmp", "Assets\\OpenCV\\goryokaku.jpg", "Assets\\OpenCV\\hand_p.jpg", "Assets\\OpenCV\\left01.jpg", "Assets\\OpenCV\\left02.jpg", "Assets\\OpenCV\\left03.jpg", "Assets\\OpenCV\\left04.jpg", "Assets\\OpenCV\\left05.jpg", "Assets\\OpenCV\\left06.jpg", "Assets\\OpenCV\\left07.jpg", "Assets\\OpenCV\\left08.jpg", "Assets\\OpenCV\\left09.jpg", "Assets\\OpenCV\\left10.jpg", "Assets\\OpenCV\\left11.jpg", "Assets\\OpenCV\\left12.jpg", "Assets\\OpenCV\\left13.jpg", "Assets\\OpenCV\\lenna.png", "Assets\\OpenCV\\lenna511.png", "Assets\\OpenCV\\maltese.jpg", "Assets\\OpenCV\\Mandrill.bmp", "Assets\\OpenCV\\match1.png", "Assets\\OpenCV\\match2.png", "Assets\\OpenCV\\penguin1.png", "Assets\\OpenCV\\penguin1b.png", "Assets\\OpenCV\\penguin2.png", "Assets\\OpenCV\\pentagon.png", "Assets\\OpenCV\\pic1.png", "Assets\\OpenCV\\pic2.png", "Assets\\OpenCV\\pic3.png", "Assets\\OpenCV\\pic4.png", "Assets\\OpenCV\\pic5.png", "Assets\\OpenCV\\pic6.png", "Assets\\OpenCV\\right01.jpg", "Assets\\OpenCV\\right02.jpg", "Assets\\OpenCV\\right03.jpg", "Assets\\OpenCV\\right04.jpg", "Assets\\OpenCV\\right05.jpg", "Assets\\OpenCV\\right06.jpg", "Assets\\OpenCV\\right07.jpg", "Assets\\OpenCV\\right08.jpg", "Assets\\OpenCV\\right09.jpg", "Assets\\OpenCV\\right10.jpg", "Assets\\OpenCV\\right11.jpg", "Assets\\OpenCV\\right12.jpg", "Assets\\OpenCV\\right13.jpg", "Assets\\OpenCV\\shapes.png", "Assets\\OpenCV\\space_shuttle.jpg", "Assets\\OpenCV\\tsukuba_left.png", "Assets\\OpenCV\\tsukuba_right.png", "Assets\\OpenCV\\very_old_newspaper.png", "Assets\\OpenCV\\walkman.jpg", "Assets\\OpenCV\\yalta.jpg", "Assets\\Person\\009445.jpg", "Assets\\Person\\009453.jpg", "Assets\\Person\\009456.jpg", "Assets\\Person\\1.jpg", "Assets\\Person\\Image1.jpg", "Assets\\Person\\Image4.jpg", "Assets\\Person\\Image5.jpg", "Assets\\Person\\woman.jpg", "Assets\\Person\\yolov6-inference-cycling.jpg", "Assets\\Person\\yolov6-inference-soccer.jpg", "Assets\\pill_bag\\pill_bag_001.png", "Assets\\pill_bag\\pill_bag_002.png", "Assets\\pill_bag\\pill_bag_003.png", "Assets\\pill_bag\\pill_bag_004.png", "Assets\\pill_bag\\pill_bag_005.png", "Assets\\pill_bag\\pill_bag_006.png", "Assets\\pill_bag\\pill_bag_007.png", "Assets\\pill_bag\\pill_bag_008.png", "Assets\\pill_bag\\pill_bag_009.png", "Assets\\pill_bag\\pill_bag_010.png", "Assets\\pill_magnesium_crack\\pill_magnesium_crack_280.png", "Assets\\pill_magnesium_crack\\pill_magnesium_crack_281.png", "Assets\\pill_magnesium_crack\\pill_magnesium_crack_282.png", "Assets\\pill_magnesium_crack\\pill_magnesium_crack_283.png", "Assets\\pill_magnesium_crack\\pill_magnesium_crack_284.png", "Assets\\pill_magnesium_crack\\pill_magnesium_crack_285.png", "Assets\\pill_magnesium_crack\\pill_magnesium_crack_286.png", "Assets\\pill_magnesium_crack\\pill_magnesium_crack_287.png", "Assets\\pill_magnesium_crack\\pill_magnesium_crack_288.png", "Assets\\pill_magnesium_crack\\pill_magnesium_crack_289.png", "Assets\\radius-gauges\\radius-gauges-00.png", "Assets\\radius-gauges\\radius-gauges-01.png", "Assets\\radius-gauges\\radius-gauges-02.png", "Assets\\radius-gauges\\radius-gauges-03.png", "Assets\\radius-gauges\\radius-gauges-04.png", "Assets\\radius-gauges\\radius-gauges-05.png", "Assets\\radius-gauges\\radius-gauges-06.png", "Assets\\radius-gauges\\radius-gauges-07.png", "Assets\\radius-gauges\\radius-gauges-08.png", "Assets\\radius-gauges\\radius-gauges-09.png", "Assets\\radius-gauges\\radius-gauges-10.png", "Assets\\radius-gauges\\radius-gauges-11.png", "Assets\\radius-gauges\\radius-gauges-12.png", "Assets\\radius-gauges\\radius-gauges-13.png", "Assets\\radius-gauges\\radius-gauges-14.png", "Assets\\radius-gauges\\radius-gauges-15.png", "Assets\\radius-gauges\\radius-gauges-16.png", "Assets\\radius-gauges\\radius-gauges-17.png", "Assets\\radius-gauges\\radius-gauges-18.png", "Assets\\radius-gauges\\radius-gauges-19.png", "Assets\\radius-gauges\\radius-gauges-20.png", "Assets\\rice.png", "Assets\\zhifang_ball.png"]}, "ROI": {"$id": "22", "$type": "H.VisionMaster.NodeData.ROIPresenters.FromROI, H.VisionMaster.NodeData", "ID": "7dd1e8f6-30b2-431c-953c-a8bb6a0ca349", "Name": "继承"}, "FromROI": {"$ref": "22"}, "DrawROI": {"$id": "23", "$type": "H.VisionMaster.NodeData.ROIPresenters.DrawROI, H.VisionMaster.NodeData", "Rect": "Empty", "ID": "c8dbf9e2-64ed-40bd-ad10-5def157cff3c", "Name": "绘制"}, "InputROI": {"$id": "24", "$type": "H.VisionMaster.NodeData.ROIPresenters.InputROI, H.VisionMaster.NodeData", "ID": "bb826b65-0cd7-499c-8764-8181866b5c18", "Name": "输入"}, "PreviewMillisecondsDelay": 0, "InvokeMillisecondsDelay": 0, "FlagLength": 10.0, "State": "Success", "TimeSpan": "00:00:00.0963126", "Message": "运行成功", "DiagramData": {"$ref": "20"}, "Text": "本地图像源", "FontSize": 12.0, "PortDatas": {"$type": "System.Collections.Generic.List`1[[H.Controls.Diagram.Datas.IPortData, H.Controls.Diagram]], System.Private.CoreLib", "$values": [{"$id": "25", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "State": "Wait", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Top", "NodeID": "7290db37-a490-4cc1-848f-4604f68a1566", "PortType": "Input", "ID": "961bc524-2b29-42e5-ad71-be3f86946101"}, {"$id": "26", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "State": "Wait", "Message": "运行成功", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Bottom", "NodeID": "7290db37-a490-4cc1-848f-4604f68a1566", "PortType": "OutPut", "ID": "b66c4bd6-958e-47e9-ae28-98950f542446"}, {"$id": "27", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "State": "Wait", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "NodeID": "7290db37-a490-4cc1-848f-4604f68a1566", "PortType": "Input", "ID": "57e5530a-3dbe-4aa1-a027-9eb39b93cdfe"}, {"$id": "28", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "State": "Wait", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Right", "NodeID": "7290db37-a490-4cc1-848f-4604f68a1566", "PortType": "OutPut", "ID": "be847c58-7e64-4f04-86bc-0d620ed9387b"}]}, "IsTemplate": false, "Height": 35.0, "Width": 120.0, "Fill": "#FFFFFFFF", "CornerRadius": 2.0, "Location": "487.0074074074074,538.5555555555554", "ID": "7290db37-a490-4cc1-848f-4604f68a1566", "Name": "本地图像源", "Icon": ""}, {"$id": "29", "$type": "H.VisionMaster.OpenCV.NodeDatas.Basic.HSVInRange, H.VisionMaster.OpenCV", "ImageColorPickerPresenter": {"$id": "30", "$type": "H.VisionMaster.NodeData.ImageColorPickerPresenter, H.VisionMaster.NodeData", "Color": "#FFD2BC45"}, "hRange": 11, "sRange": 90, "vRange": 16, "ROI": {"$id": "31", "$type": "H.VisionMaster.NodeData.ROIPresenters.FromROI, H.VisionMaster.NodeData", "ID": "9266fe0b-709c-4a92-b77b-56ba3d51b317", "Name": "继承"}, "FromROI": {"$ref": "31"}, "DrawROI": {"$id": "32", "$type": "H.VisionMaster.NodeData.ROIPresenters.DrawROI, H.VisionMaster.NodeData", "Rect": "Empty", "ID": "3873c84b-8542-44d7-b836-e18fb6bbb3e2", "Name": "绘制"}, "InputROI": {"$id": "33", "$type": "H.VisionMaster.NodeData.ROIPresenters.InputROI, H.VisionMaster.NodeData", "ID": "ea9e8226-8dee-4701-8665-aaf955d5e57d", "Name": "输入"}, "PreviewMillisecondsDelay": 0, "InvokeMillisecondsDelay": 0, "FlagLength": 10.0, "State": "Success", "TimeSpan": "00:00:00.0210549", "Message": "运行成功", "DiagramData": {"$ref": "20"}, "Text": "HSV二值分割", "FontSize": 12.0, "PortDatas": {"$type": "System.Collections.Generic.List`1[[H.Controls.Diagram.Datas.IPortData, H.Controls.Diagram]], System.Private.CoreLib", "$values": [{"$id": "34", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "State": "Wait", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Top", "NodeID": "cac2db3b-ea6e-4092-99c1-6c7f747a60a8", "PortType": "Input", "ID": "abd6f416-4331-4219-973b-287888598c96"}, {"$id": "35", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "State": "Wait", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Bottom", "NodeID": "cac2db3b-ea6e-4092-99c1-6c7f747a60a8", "PortType": "OutPut", "ID": "cdb9387c-c81c-41af-b7a9-8b919d4a5609"}, {"$id": "36", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "State": "Wait", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "NodeID": "cac2db3b-ea6e-4092-99c1-6c7f747a60a8", "PortType": "Input", "ID": "7d06832b-e500-4f33-b080-42cedd765fc8"}, {"$id": "37", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "State": "Wait", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Right", "NodeID": "cac2db3b-ea6e-4092-99c1-6c7f747a60a8", "PortType": "OutPut", "ID": "09abae1e-96b3-4bc1-8987-d70488b45c12"}]}, "IsTemplate": false, "Height": 35.0, "Width": 120.0, "Fill": "#FFFFFFFF", "IsSelected": true, "CornerRadius": 2.0, "Location": "487.0074074074074,627.5555555555554", "ID": "cac2db3b-ea6e-4092-99c1-6c7f747a60a8", "Name": "HSV二值分割", "Icon": ""}]}, "LinkDatas": {"$type": "System.Collections.Generic.List`1[[H.Controls.Diagram.Datas.ILinkData, H.Controls.Diagram]], System.Private.CoreLib", "$values": [{"$id": "38", "$type": "H.Controls.Diagram.Presenter.LinkDatas.FlowableLinkData, H.Controls.Diagram.Presenter", "State": "Wait", "FontSize": 12.0, "Stroke": "#FFFFA500", "FromNodeID": "7290db37-a490-4cc1-848f-4604f68a1566", "ToNodeID": "cac2db3b-ea6e-4092-99c1-6c7f747a60a8", "FromPortID": "b66c4bd6-958e-47e9-ae28-98950f542446", "ToPortID": "abd6f416-4331-4219-973b-287888598c96", "ID": "ef6acb75-36f2-4028-817c-14790461b7c6", "Name": "连线"}]}}, "ID": "16af6e43-e1cc-4d12-b52b-d0be059038b2"}, {"$id": "39", "$type": "H.App.VisionMaster.OpenCV.DiagramDatas.OpenCVVisionDiagramData, H.App.VisionMaster.OpenCV", "RunModeResult": "True", "UseFlowableSelectToRunning": true, "Name": "机器视觉流程", "Width": 1000.0, "Height": 1500.0, "Location": "1000,650", "Message": "运行成功", "Datas": {"$type": "H.Controls.Diagram.Presenter.DiagramDatas.Base.Datas, H.Controls.Diagram.Presenter", "NodeDatas": {"$type": "System.Collections.Generic.List`1[[H.Controls.Diagram.Datas.INodeData, H.Controls.Diagram]], System.Private.CoreLib", "$values": [{"$id": "40", "$type": "H.VisionMaster.OpenCV.NodeDatas.Src.SrcImageFilesNodeData, H.VisionMaster.OpenCV", "PixelWidth": 752, "PixelHeight": 480, "ImageColorType": 16, "UseAutoSwitch": true, "SrcFilePath": "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_06.png", "SrcFilePaths": {"$type": "System.Collections.ObjectModel.ObservableCollection`1[[System.String, System.Private.CoreLib]], System.ObjectModel", "$values": ["Assets\\BaseImages\\a01.png", "Assets\\BaseImages\\alpha1.png", "Assets\\BaseImages\\alpha2.png", "Assets\\BaseImages\\angio-part.png", "Assets\\BaseImages\\atoms.png", "Assets\\BaseImages\\audi2.png", "Assets\\BaseImages\\autobahn.png", "Assets\\BaseImages\\b5_1.png", "Assets\\BaseImages\\bga_14x14_defects.png", "Assets\\BaseImages\\bga_14x14_model.png", "Assets\\BaseImages\\bitrot.tif", "Assets\\BaseImages\\bk45.png", "Assets\\BaseImages\\bottle2.png", "Assets\\BaseImages\\brycecanyon1.png", "Assets\\BaseImages\\cable1.png", "Assets\\BaseImages\\cable2.png", "Assets\\BaseImages\\caltab.png", "Assets\\BaseImages\\can.png", "Assets\\BaseImages\\can_with_grid.png", "Assets\\BaseImages\\circle_plate.png", "Assets\\BaseImages\\circular_barcode.png", "Assets\\BaseImages\\claudia.png", "Assets\\BaseImages\\clip.png", "Assets\\BaseImages\\combine.png", "Assets\\BaseImages\\crystal.png", "Assets\\BaseImages\\die_on_chip.png", "Assets\\BaseImages\\die_pads.png", "Assets\\BaseImages\\dot_print_slanted.png", "Assets\\BaseImages\\double_circle.png", "Assets\\BaseImages\\earth.png", "Assets\\BaseImages\\ed_g.png", "Assets\\BaseImages\\egypt1.png", "Assets\\BaseImages\\engraved.png", "Assets\\BaseImages\\fabrik.png", "Assets\\BaseImages\\fin1.png", "Assets\\BaseImages\\fin2.png", "Assets\\BaseImages\\fin3.png", "Assets\\BaseImages\\fingerprint.png", "Assets\\BaseImages\\for5.png", "Assets\\BaseImages\\for6.png", "Assets\\BaseImages\\forest_air1.png", "Assets\\BaseImages\\forest_road.png", "Assets\\BaseImages\\fuse.png", "Assets\\BaseImages\\glasses_polarized.png", "Assets\\BaseImages\\graph_paper.png", "Assets\\BaseImages\\green-dot.png", "Assets\\BaseImages\\green-dots.png", "Assets\\BaseImages\\horses.png", "Assets\\BaseImages\\hull.png", "Assets\\BaseImages\\ic.png", "Assets\\BaseImages\\ic0.png", "Assets\\BaseImages\\ic1.png", "Assets\\BaseImages\\ic2.png", "Assets\\BaseImages\\ic3.png", "Assets\\BaseImages\\ic_pin.png", "Assets\\BaseImages\\keypad.png", "Assets\\BaseImages\\landmarks.png", "Assets\\BaseImages\\largebw1.tif", "Assets\\BaseImages\\letters.png", "Assets\\BaseImages\\lynx.png", "Assets\\BaseImages\\lynx_mask.png", "Assets\\BaseImages\\marks.png", "Assets\\BaseImages\\meningg5.png", "Assets\\BaseImages\\meningg6.png", "Assets\\BaseImages\\monkey.png", "Assets\\BaseImages\\montery.png", "Assets\\BaseImages\\mreut.png", "Assets\\BaseImages\\mreut4_3.png", "Assets\\BaseImages\\mreut_dgm_2.0.tif", "Assets\\BaseImages\\mreut_hill.png", "Assets\\BaseImages\\mreut_y.png", "Assets\\BaseImages\\multiple_dies_01.png", "Assets\\BaseImages\\multiple_dies_02.png", "Assets\\BaseImages\\mvtec_logo.png", "Assets\\BaseImages\\needle1.png", "Assets\\BaseImages\\numbers_scale.png", "Assets\\BaseImages\\olympic_stadium.png", "Assets\\BaseImages\\pads.png", "Assets\\BaseImages\\particle.png", "Assets\\BaseImages\\patras.png", "Assets\\BaseImages\\pcb.png", "Assets\\BaseImages\\pcb_color.png", "Assets\\BaseImages\\pcb_layout.png", "Assets\\BaseImages\\pellets.png", "Assets\\BaseImages\\pioneer.png", "Assets\\BaseImages\\plan_01.png", "Assets\\BaseImages\\plan_02.png", "Assets\\BaseImages\\plit2.png", "Assets\\BaseImages\\progres.png", "Assets\\BaseImages\\pumpe.png", "Assets\\BaseImages\\punched_holes.png", "Assets\\BaseImages\\razors1.png", "Assets\\BaseImages\\razors2.png", "Assets\\BaseImages\\rim.png", "Assets\\BaseImages\\rings_and_nuts.png", "Assets\\BaseImages\\screw_thread.png", "Assets\\BaseImages\\surface_scratch.png", "Assets\\BaseImages\\tooth_rim.png", "Assets\\BaseImages\\traffic1.png", "Assets\\BaseImages\\traffic2.png", "Assets\\BaseImages\\vessel.png", "Assets\\BaseImages\\woodring.png", "Assets\\BaseImages\\wood_knots.png", "Assets\\BaseImages\\zeiss1.png", "Assets\\board\\board-01.png", "Assets\\board\\board-02.png", "Assets\\board\\board-03.png", "Assets\\board\\board-04.png", "Assets\\board\\board-05.png", "Assets\\board\\board-06.png", "Assets\\board\\board-07.png", "Assets\\board\\board-08.png", "Assets\\board\\board-09.png", "Assets\\board\\board-10.png", "Assets\\board\\board-11.png", "Assets\\board\\board-12.png", "Assets\\board\\board-13.png", "Assets\\board\\board-14.png", "Assets\\board\\board-15.png", "Assets\\board\\board-16.png", "Assets\\board\\board-17.png", "Assets\\board\\board-18.png", "Assets\\board\\board-19.png", "Assets\\board\\board-20.png", "Assets\\board\\board_01.png", "Assets\\board\\board_02.png", "Assets\\board\\board_03.png", "Assets\\board\\board_04.png", "Assets\\board\\board_05.png", "Assets\\board\\board_06.png", "Assets\\board\\board_07.png", "Assets\\board\\board_08.png", "Assets\\board\\board_09.png", "Assets\\bottle_crate\\bottle_crate_01.png", "Assets\\bottle_crate\\bottle_crate_02.png", "Assets\\bottle_crate\\bottle_crate_03.png", "Assets\\bottle_crate\\bottle_crate_04.png", "Assets\\bottle_crate\\bottle_crate_05.png", "Assets\\bottle_crate\\bottle_crate_06.png", "Assets\\bottle_crate\\bottle_crate_07.png", "Assets\\bottle_crate\\bottle_crate_08.png", "Assets\\bottle_crate\\bottle_crate_09.png", "Assets\\bottle_crate\\bottle_crate_10.png", "Assets\\bottle_crate\\bottle_crate_11.png", "Assets\\bottle_crate\\bottle_crate_12.png", "Assets\\bottle_crate\\bottle_crate_13.png", "Assets\\bottle_crate\\bottle_crate_14.png", "Assets\\bottle_crate\\bottle_crate_15.png", "Assets\\bottle_crate\\bottle_crate_16.png", "Assets\\bottle_crate\\bottle_crate_17.png", "Assets\\bottle_crate\\bottle_crate_18.png", "Assets\\bottle_crate\\bottle_crate_19.png", "Assets\\bottle_crate\\bottle_crate_20.png", "Assets\\bottle_crate\\bottle_crate_21.png", "Assets\\bottle_crate\\bottle_crate_22.png", "Assets\\bottle_crate\\bottle_crate_23.png", "Assets\\bottle_crate\\bottle_crate_24.png", "Assets\\box.bmp", "Assets\\car_door\\car_door_01.png", "Assets\\car_door\\car_door_02.png", "Assets\\car_door\\car_door_03.png", "Assets\\car_door\\car_door_04.png", "Assets\\car_door\\car_door_05.png", "Assets\\car_door\\car_door_06.png", "Assets\\car_door\\car_door_07.png", "Assets\\car_door\\car_door_08.png", "Assets\\car_door\\car_door_09.png", "Assets\\car_door\\car_door_10.png", "Assets\\car_door\\car_door_11.png", "Assets\\car_door\\car_door_12.png", "Assets\\car_door\\car_door_13.png", "Assets\\car_door\\car_door_14.png", "Assets\\car_door\\car_door_15.png", "Assets\\car_door\\car_door_16.png", "Assets\\car_door\\car_door_17.png", "Assets\\car_door\\car_door_18.png", "Assets\\car_door\\car_door_19.png", "Assets\\car_door\\car_door_20.png", "Assets\\car_door\\car_door_21.png", "Assets\\car_door\\car_door_calib_plate.png", "Assets\\car_door\\car_door_init_pose.png", "Assets\\green.jpg", "Assets\\multi_view_bitholder_cam\\multi_view_bitholder_cam_0_1.png", "Assets\\multi_view_bitholder_cam\\multi_view_bitholder_cam_0_10.png", "Assets\\multi_view_bitholder_cam\\multi_view_bitholder_cam_0_2.png", "Assets\\multi_view_bitholder_cam\\multi_view_bitholder_cam_0_3.png", "Assets\\multi_view_bitholder_cam\\multi_view_bitholder_cam_0_4.png", "Assets\\multi_view_bitholder_cam\\multi_view_bitholder_cam_0_7.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_01.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_02.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_03.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_04.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_05.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_06.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_07.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_08.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_09.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_10.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_11.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_12.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_13.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_14.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_15.png", "Assets\\OpenCV\\00.JPG", "Assets\\OpenCV\\01.JPG", "Assets\\OpenCV\\02.JPG", "Assets\\OpenCV\\16bit.png", "Assets\\OpenCV\\aruco_markers_photo.jpg", "Assets\\OpenCV\\aruco_markers_source.jpg", "Assets\\OpenCV\\asahiyama.jpg", "Assets\\OpenCV\\Balloon.png", "Assets\\OpenCV\\binarization_sample.bmp", "Assets\\OpenCV\\box.png", "Assets\\OpenCV\\box_in_scene.png", "Assets\\OpenCV\\cake.bmp", "Assets\\OpenCV\\cat.jpg", "Assets\\OpenCV\\circle.png", "Assets\\OpenCV\\cvmorph.png", "Assets\\OpenCV\\fruits.jpg", "Assets\\OpenCV\\Girl.bmp", "Assets\\OpenCV\\goryokaku.jpg", "Assets\\OpenCV\\hand_p.jpg", "Assets\\OpenCV\\left01.jpg", "Assets\\OpenCV\\left02.jpg", "Assets\\OpenCV\\left03.jpg", "Assets\\OpenCV\\left04.jpg", "Assets\\OpenCV\\left05.jpg", "Assets\\OpenCV\\left06.jpg", "Assets\\OpenCV\\left07.jpg", "Assets\\OpenCV\\left08.jpg", "Assets\\OpenCV\\left09.jpg", "Assets\\OpenCV\\left10.jpg", "Assets\\OpenCV\\left11.jpg", "Assets\\OpenCV\\left12.jpg", "Assets\\OpenCV\\left13.jpg", "Assets\\OpenCV\\lenna.png", "Assets\\OpenCV\\lenna511.png", "Assets\\OpenCV\\maltese.jpg", "Assets\\OpenCV\\Mandrill.bmp", "Assets\\OpenCV\\match1.png", "Assets\\OpenCV\\match2.png", "Assets\\OpenCV\\penguin1.png", "Assets\\OpenCV\\penguin1b.png", "Assets\\OpenCV\\penguin2.png", "Assets\\OpenCV\\pentagon.png", "Assets\\OpenCV\\pic1.png", "Assets\\OpenCV\\pic2.png", "Assets\\OpenCV\\pic3.png", "Assets\\OpenCV\\pic4.png", "Assets\\OpenCV\\pic5.png", "Assets\\OpenCV\\pic6.png", "Assets\\OpenCV\\right01.jpg", "Assets\\OpenCV\\right02.jpg", "Assets\\OpenCV\\right03.jpg", "Assets\\OpenCV\\right04.jpg", "Assets\\OpenCV\\right05.jpg", "Assets\\OpenCV\\right06.jpg", "Assets\\OpenCV\\right07.jpg", "Assets\\OpenCV\\right08.jpg", "Assets\\OpenCV\\right09.jpg", "Assets\\OpenCV\\right10.jpg", "Assets\\OpenCV\\right11.jpg", "Assets\\OpenCV\\right12.jpg", "Assets\\OpenCV\\right13.jpg", "Assets\\OpenCV\\shapes.png", "Assets\\OpenCV\\space_shuttle.jpg", "Assets\\OpenCV\\tsukuba_left.png", "Assets\\OpenCV\\tsukuba_right.png", "Assets\\OpenCV\\very_old_newspaper.png", "Assets\\OpenCV\\walkman.jpg", "Assets\\OpenCV\\yalta.jpg", "Assets\\Person\\009445.jpg", "Assets\\Person\\009453.jpg", "Assets\\Person\\009456.jpg", "Assets\\Person\\1.jpg", "Assets\\Person\\Image1.jpg", "Assets\\Person\\Image4.jpg", "Assets\\Person\\Image5.jpg", "Assets\\Person\\woman.jpg", "Assets\\Person\\yolov6-inference-cycling.jpg", "Assets\\Person\\yolov6-inference-soccer.jpg", "Assets\\pill_bag\\pill_bag_001.png", "Assets\\pill_bag\\pill_bag_002.png", "Assets\\pill_bag\\pill_bag_003.png", "Assets\\pill_bag\\pill_bag_004.png", "Assets\\pill_bag\\pill_bag_005.png", "Assets\\pill_bag\\pill_bag_006.png", "Assets\\pill_bag\\pill_bag_007.png", "Assets\\pill_bag\\pill_bag_008.png", "Assets\\pill_bag\\pill_bag_009.png", "Assets\\pill_bag\\pill_bag_010.png", "Assets\\pill_magnesium_crack\\pill_magnesium_crack_280.png", "Assets\\pill_magnesium_crack\\pill_magnesium_crack_281.png", "Assets\\pill_magnesium_crack\\pill_magnesium_crack_282.png", "Assets\\pill_magnesium_crack\\pill_magnesium_crack_283.png", "Assets\\pill_magnesium_crack\\pill_magnesium_crack_284.png", "Assets\\pill_magnesium_crack\\pill_magnesium_crack_285.png", "Assets\\pill_magnesium_crack\\pill_magnesium_crack_286.png", "Assets\\pill_magnesium_crack\\pill_magnesium_crack_287.png", "Assets\\pill_magnesium_crack\\pill_magnesium_crack_288.png", "Assets\\pill_magnesium_crack\\pill_magnesium_crack_289.png", "Assets\\radius-gauges\\radius-gauges-00.png", "Assets\\radius-gauges\\radius-gauges-01.png", "Assets\\radius-gauges\\radius-gauges-02.png", "Assets\\radius-gauges\\radius-gauges-03.png", "Assets\\radius-gauges\\radius-gauges-04.png", "Assets\\radius-gauges\\radius-gauges-05.png", "Assets\\radius-gauges\\radius-gauges-06.png", "Assets\\radius-gauges\\radius-gauges-07.png", "Assets\\radius-gauges\\radius-gauges-08.png", "Assets\\radius-gauges\\radius-gauges-09.png", "Assets\\radius-gauges\\radius-gauges-10.png", "Assets\\radius-gauges\\radius-gauges-11.png", "Assets\\radius-gauges\\radius-gauges-12.png", "Assets\\radius-gauges\\radius-gauges-13.png", "Assets\\radius-gauges\\radius-gauges-14.png", "Assets\\radius-gauges\\radius-gauges-15.png", "Assets\\radius-gauges\\radius-gauges-16.png", "Assets\\radius-gauges\\radius-gauges-17.png", "Assets\\radius-gauges\\radius-gauges-18.png", "Assets\\radius-gauges\\radius-gauges-19.png", "Assets\\radius-gauges\\radius-gauges-20.png", "Assets\\rice.png", "Assets\\zhifang_ball.png"]}, "ROI": {"$id": "41", "$type": "H.VisionMaster.NodeData.ROIPresenters.FromROI, H.VisionMaster.NodeData", "ID": "7dd1e8f6-30b2-431c-953c-a8bb6a0ca349", "Name": "继承"}, "FromROI": {"$ref": "41"}, "DrawROI": {"$id": "42", "$type": "H.VisionMaster.NodeData.ROIPresenters.DrawROI, H.VisionMaster.NodeData", "Rect": "Empty", "ID": "c8dbf9e2-64ed-40bd-ad10-5def157cff3c", "Name": "绘制"}, "InputROI": {"$id": "43", "$type": "H.VisionMaster.NodeData.ROIPresenters.InputROI, H.VisionMaster.NodeData", "ID": "bb826b65-0cd7-499c-8764-8181866b5c18", "Name": "输入"}, "PreviewMillisecondsDelay": 0, "InvokeMillisecondsDelay": 0, "FlagLength": 10.0, "State": "Success", "TimeSpan": "00:00:00.0474958", "Message": "运行成功", "DiagramData": {"$ref": "39"}, "Text": "本地图像源", "FontSize": 12.0, "PortDatas": {"$type": "System.Collections.Generic.List`1[[H.Controls.Diagram.Datas.IPortData, H.Controls.Diagram]], System.Private.CoreLib", "$values": [{"$id": "44", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "State": "Wait", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Top", "NodeID": "7290db37-a490-4cc1-848f-4604f68a1566", "PortType": "Input", "ID": "961bc524-2b29-42e5-ad71-be3f86946101"}, {"$id": "45", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "State": "Wait", "Message": "运行成功", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Bottom", "NodeID": "7290db37-a490-4cc1-848f-4604f68a1566", "PortType": "OutPut", "ID": "b66c4bd6-958e-47e9-ae28-98950f542446"}, {"$id": "46", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "State": "Wait", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "NodeID": "7290db37-a490-4cc1-848f-4604f68a1566", "PortType": "Input", "ID": "57e5530a-3dbe-4aa1-a027-9eb39b93cdfe"}, {"$id": "47", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "State": "Wait", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Right", "NodeID": "7290db37-a490-4cc1-848f-4604f68a1566", "PortType": "OutPut", "ID": "be847c58-7e64-4f04-86bc-0d620ed9387b"}]}, "IsTemplate": false, "Height": 35.0, "Width": 120.0, "Fill": "#FFFFFFFF", "CornerRadius": 2.0, "Location": "487.0074074074074,537.8148148148148", "ID": "7290db37-a490-4cc1-848f-4604f68a1566", "Name": "本地图像源", "Icon": ""}, {"$id": "48", "$type": "H.VisionMaster.OpenCV.NodeDatas.Basic.HSVInRange, H.VisionMaster.OpenCV", "ImageColorPickerPresenter": {"$id": "49", "$type": "H.VisionMaster.NodeData.ImageColorPickerPresenter, H.VisionMaster.NodeData", "Color": "#FF232323"}, "hRange": 12, "sRange": 0, "vRange": 34, "ROI": {"$id": "50", "$type": "H.VisionMaster.NodeData.ROIPresenters.FromROI, H.VisionMaster.NodeData", "ID": "9266fe0b-709c-4a92-b77b-56ba3d51b317", "Name": "继承"}, "FromROI": {"$ref": "50"}, "DrawROI": {"$id": "51", "$type": "H.VisionMaster.NodeData.ROIPresenters.DrawROI, H.VisionMaster.NodeData", "Rect": "Empty", "ID": "3873c84b-8542-44d7-b836-e18fb6bbb3e2", "Name": "绘制"}, "InputROI": {"$id": "52", "$type": "H.VisionMaster.NodeData.ROIPresenters.InputROI, H.VisionMaster.NodeData", "ID": "ea9e8226-8dee-4701-8665-aaf955d5e57d", "Name": "输入"}, "PreviewMillisecondsDelay": 0, "InvokeMillisecondsDelay": 0, "FlagLength": 10.0, "State": "Success", "TimeSpan": "00:00:00.0227201", "Message": "运行成功", "DiagramData": {"$ref": "39"}, "Text": "HSV二值分割", "FontSize": 12.0, "PortDatas": {"$type": "System.Collections.Generic.List`1[[H.Controls.Diagram.Datas.IPortData, H.Controls.Diagram]], System.Private.CoreLib", "$values": [{"$id": "53", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "State": "Wait", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Top", "NodeID": "cac2db3b-ea6e-4092-99c1-6c7f747a60a8", "PortType": "Input", "ID": "abd6f416-4331-4219-973b-287888598c96"}, {"$id": "54", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "State": "Wait", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Bottom", "NodeID": "cac2db3b-ea6e-4092-99c1-6c7f747a60a8", "PortType": "OutPut", "ID": "cdb9387c-c81c-41af-b7a9-8b919d4a5609"}, {"$id": "55", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "State": "Wait", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "NodeID": "cac2db3b-ea6e-4092-99c1-6c7f747a60a8", "PortType": "Input", "ID": "7d06832b-e500-4f33-b080-42cedd765fc8"}, {"$id": "56", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "State": "Wait", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Right", "NodeID": "cac2db3b-ea6e-4092-99c1-6c7f747a60a8", "PortType": "OutPut", "ID": "09abae1e-96b3-4bc1-8987-d70488b45c12"}]}, "IsTemplate": false, "Height": 35.0, "Width": 120.0, "Fill": "#FFFFFFFF", "CornerRadius": 2.0, "Location": "487.0074074074074,627.5555555555554", "ID": "cac2db3b-ea6e-4092-99c1-6c7f747a60a8", "Name": "HSV二值分割", "Icon": ""}]}, "LinkDatas": {"$type": "System.Collections.Generic.List`1[[H.Controls.Diagram.Datas.ILinkData, H.Controls.Diagram]], System.Private.CoreLib", "$values": [{"$id": "57", "$type": "H.Controls.Diagram.Presenter.LinkDatas.FlowableLinkData, H.Controls.Diagram.Presenter", "State": "Wait", "FontSize": 12.0, "Stroke": "#FFFFA500", "FromNodeID": "7290db37-a490-4cc1-848f-4604f68a1566", "ToNodeID": "cac2db3b-ea6e-4092-99c1-6c7f747a60a8", "FromPortID": "b66c4bd6-958e-47e9-ae28-98950f542446", "ToPortID": "abd6f416-4331-4219-973b-287888598c96", "ID": "ef6acb75-36f2-4028-817c-14790461b7c6", "Name": "连线"}]}}, "ID": "16af6e43-e1cc-4d12-b52b-d0be059038b2"}]}