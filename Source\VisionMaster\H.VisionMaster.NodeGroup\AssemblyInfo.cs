// Copyright (c) HeBianGu Authors. All Rights Reserved. 
// Author: HeBianGu 
// Github: https://github.com/HeBianGu/WPF-Control 
// Document: https://hebiangu.github.io/WPF-Control-Docs  
// QQ:908293466 Group:971261058 
// bilibili: https://space.bilibili.com/370266611 
// Licensed under the MIT License (the "License")

using System.Windows.Markup;

[assembly: ThemeInfo(
    ResourceDictionaryLocation.None, //where theme specific resource dictionaries are located
                                     //(used if a resource is not found in the page,
                                     // or application resource dictionaries)
    ResourceDictionaryLocation.SourceAssembly //where the generic resource dictionary is located
                                              //(used if a resource is not found in the page,
                                              // app, or any theme specific resource dictionaries)
)]

[assembly: XmlnsDefinition("QQ:908293466", "H.VisionMaster.NodeGroup")]
[assembly: XmlnsPrefix("QQ:908293466", "h")]

//[assembly: XmlnsDefinition("https://github.com/HeBianGu", "H.VisionMaster.NodeGroup.NodeDatas")]
//[assembly: XmlnsDefinition("https://github.com/HeBianGu", "H.VisionMaster.NodeGroup.NodeDataGroups")]
[assembly: XmlnsDefinition("https://github.com/HeBianGu", "H.VisionMaster.NodeGroup")]
[assembly: XmlnsPrefix("https://github.com/HeBianGu", "h")]

//[assembly: XmlnsDefinition("http://schemas.microsoft.com/winfx/2006/xaml/presentation", "H.VisionMaster.NodeGroup.NodeDatas")]
//[assembly: XmlnsDefinition("http://schemas.microsoft.com/winfx/2006/xaml/presentation", "H.VisionMaster.NodeGroup.NodeDataGroups")]
[assembly: XmlnsDefinition("http://schemas.microsoft.com/winfx/2006/xaml/presentation", "H.VisionMaster.NodeGroup")]
[assembly: XmlnsPrefix("http://schemas.microsoft.com/winfx/2006/xaml/presentation", "h")]
