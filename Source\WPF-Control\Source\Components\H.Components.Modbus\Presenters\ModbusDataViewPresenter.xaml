﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:b="http://schemas.microsoft.com/xaml/behaviors"
                    xmlns:bs="clr-namespace:H.Components.Modbus.Base"
                    xmlns:local="clr-namespace:H.Components.Modbus"
                    xmlns:p="clr-namespace:H.Components.Modbus.Presenters">
    <DataTemplate DataType="{x:Type p:ModbusDataViewPresenter}">
        <DockPanel MinWidth="400">
            <DockPanel DockPanel.Dock="Top"
                       LastChildFill="False">
                <Button Command="{Binding AddCommand}"
                        Content="添加" />
                <Button Margin="1,0"
                        Command="{Binding EditCommand}"
                        CommandParameter="{Binding SelectedItem}"
                        Content="编辑" />
                <Button Margin="1,0"
                        Command="{Binding DeleteCommand}"
                        CommandParameter="{Binding SelectedItem}"
                        Content="删除" />
                <Button Command="{Binding ClearCommand}"
                        Content="清空" />
                <Button Command="{Binding StartCommand}"
                        Content="启动监控"
                        DockPanel.Dock="Right" />
                <Button Margin="1,0"
                        Command="{Binding StopCommand}"
                        Content="停止监控"
                        DockPanel.Dock="Right" />
                <ContentPresenter Margin="15,0"
                                  VerticalAlignment="Center"
                                  Content="{Binding DataService.State}"
                                  DockPanel.Dock="Right" />
            </DockPanel>
            <DockPanel>
                <DataGrid AutoGenerateColumns="False"
                          IsReadOnly="True"
                          ItemsSource="{Binding DataService.Collection}"
                          SelectedItem="{Binding SelectedItem}">
                    <DataGrid.Columns>
                        <DataGridTextColumn MinWidth="120"
                                            Binding="{Binding Name}"
                                            Header="名称" />
                        <DataGridTextColumn MinWidth="120"
                                            Binding="{Binding Ip}"
                                            Header="地址" />
                        <DataGridTextColumn MinWidth="120"
                                            Binding="{Binding Port}"
                                            Header="端口号" />
                        <DataGridTextColumn MinWidth="120"
                                            Binding="{Binding SlaveAddress}"
                                            Header="Slave地址" />
                        <DataGridTextColumn MinWidth="120"
                                            Binding="{Binding StartAddress}"
                                            Header="读取位置" />
                        <DataGridTextColumn MinWidth="120"
                                            Binding="{Binding NumberOfPoints}"
                                            Header="读取长度" />
                        <DataGridTemplateColumn MinWidth="120"
                                                Header="状态">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <ContentPresenter Content="{Binding State}" />
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        <DataGridTextColumn Width="Auto"
                                            MinWidth="120"
                                            Binding="{Binding UpdateTime}"
                                            Header="更新时间" />
                        <DataGridTemplateColumn Width="*"
                                                Header="值">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <ContentPresenter Content="{Binding Value}" />
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        <DataGridTemplateColumn Width="*"
                                                Header="信息">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding Message}"
                                               TextWrapping="Wrap"
                                               ToolTip="{Binding Message}" />
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </DockPanel>
        </DockPanel>
    </DataTemplate>
</ResourceDictionary>