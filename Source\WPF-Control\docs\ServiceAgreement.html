<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WPF-Control 服务协议</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            color: #333;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        h2 {
            color: #3498db;
            margin-top: 30px;
        }
        .footer {
            margin-top: 50px;
            text-align: center;
            font-size: 0.9em;
            color: #7f8c8d;
        }
        a {
            color: #2980b9;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <h1>WPF-Control 开源项目服务协议</h1>
    
    <h2>1. 协议适用范围</h2>
    <p>本服务协议适用于所有使用、修改或分发由 HeBianGu 开发的 WPF-Control 开源项目（GitHub 仓库：<a href="https://github.com/HeBianGu/WPF-Control" target="_blank">https://github.com/HeBianGu/WPF-Control</a>）的用户。</p>
    
    <h2>2. 许可条款</h2>
    <p>WPF-Control 项目采用 <a href="https://opensource.org/licenses/MIT" target="_blank">MIT 许可证</a>，具体条款如下：</p>
    <ul>
        <li>允许自由使用、复制、修改、合并、出版发行、散布、再许可和/或销售本软件的副本</li>
        <li>允许将本软件用于任何目的，包括商业用途</li>
        <li>在软件和软件的所有副本中必须包含上述版权声明和本许可声明</li>
    </ul>
    
    <h2>3. 用户责任</h2>
    <p>使用本项目的用户需同意：</p>
    <ol>
        <li>不得将本项目用于任何违法用途</li>
        <li>不得移除或修改项目中的版权声明</li>
        <li>基于本项目开发的衍生作品应明确标注原始项目来源</li>
    </ol>
    
    <h2>4. 免责声明</h2>
    <p>本项目按"原样"提供，不提供任何明示或暗示的担保，包括但不限于对适销性、特定用途适用性和非侵权性的担保。在任何情况下，作者或版权持有人均不对任何索赔、损害或其他责任负责。</p>
    
    <h2>5. 贡献条款</h2>
    <p>向本项目提交贡献即表示您同意：</p>
    <ol>
        <li>您的贡献将在 MIT 许可证下授权</li>
        <li>您有权授予上述许可</li>
        <li>您的贡献不包含您无权许可的第三方代码</li>
    </ol>
    
    <h2>6. 隐私保护</h2>
    <p>本项目不收集用户数据，但通过 GitHub 平台使用时需遵守 GitHub 的隐私政策。</p>
    
    <h2>7. 协议修改</h2>
    <p>本协议可能会不定期更新，更新后的协议将在项目仓库中发布。</p>
    
    <h2>8. 法律适用</h2>
    <p>本协议受中华人民共和国法律管辖并按其解释。</p>
    
    <div class="footer">
        <p>最后更新日期：2023年11月1日</p>
        <p>© 2023 WPF-Control 项目团队 保留所有权利</p>
    </div>
</body>
</html>