﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:b="http://schemas.microsoft.com/xaml/behaviors"
                    xmlns:h="https://github.com/HeBianGu"
                    xmlns:lbc="clr-namespace:H.VisionMaster.NodeData.Base.Conditions"
                    xmlns:local="clr-namespace:H.VisionMaster.NodeData"
                    xmlns:system="clr-namespace:System;assembly=mscorlib">
    <DataTemplate DataType="{x:Type lbc:VisionPropertyConditionsPrensenter}">
        <DockPanel MinWidth="800"
                   MinHeight="400">
            <DockPanel Margin="0,5"
                       DockPanel.Dock="Top"
                       LastChildFill="False">
                <Button HorizontalAlignment="Left"
                        Command="{Binding AddCommand}"
                        Content="添加条件分支" />
            </DockPanel>
            <ListBox MinWidth="200"
                     Padding="1"
                     BorderBrush="{DynamicResource {x:Static h:BrushKeys.BorderBrush}}"
                     BorderThickness="1"
                     Cattach.ItemMargin="1 0"
                     Cattach.ItemPadding="10 0 0 0"
                     DockPanel.Dock="Left"
                     ItemsSource="{Binding PropertyConfidtions}"
                     SelectedItem="{Binding SelectedItem}">
                <ListBox.ItemTemplate>
                    <DataTemplate>
                        <DockPanel>
                            <FontIconButton Content="{x:Static FontIcons.Cancel}"
                                            DockPanel.Dock="Right"
                                            FontSize="12">
                                <b:Interaction.Behaviors>
                                    <h:ButtonRemoveItemBehavior />
                                </b:Interaction.Behaviors>
                            </FontIconButton>
                            <TextBlock VerticalAlignment="Center"
                                       Text="{Binding ID}" />
                        </DockPanel>
                    </DataTemplate>
                </ListBox.ItemTemplate>
            </ListBox>
            <Border Margin="1,0"
                    BorderBrush="{DynamicResource {x:Static h:BrushKeys.BorderBrush}}"
                    BorderThickness="1">
                <DockPanel MinWidth="600"
                           Margin="1">
                    <UniformGrid Margin="0,0"
                                 DockPanel.Dock="Top"
                                 Rows="1">
                        <BulletDecorator>
                            <BulletDecorator.Bullet>
                                <TextBlock Width="80"
                                           Text=" 名称" />
                            </BulletDecorator.Bullet>
                            <TextBox MinWidth="300"
                                     Text="{Binding SelectedItem.ID}" />
                        </BulletDecorator>
                        <BulletDecorator>
                            <BulletDecorator.Bullet>
                                <TextBlock Width="80"
                                           Text=" 规则" />
                            </BulletDecorator.Bullet>
                            <ComboBox MinWidth="300"
                                      ItemsSource="{Binding Source={h:GetEnumSource EnumType={x:Type ConditionOperate}}}"
                                      SelectedItem="{Binding SelectedItem.ConditionOperate}" />
                        </BulletDecorator>
                    </UniformGrid>
                    <UniformGrid Margin="0,1"
                                 DockPanel.Dock="Top"
                                 Rows="1">
                        <BulletDecorator>
                            <BulletDecorator.Bullet>
                                <TextBlock Width="80"
                                           Text=" 判断节点" />
                            </BulletDecorator.Bullet>
                            <ComboBox Height="Auto"
                                      MinWidth="300"
                                      Cattach.ItemHeight="Auto"
                                      Cattach.ItemHorizontalContentAlignment="Left"
                                      ItemsSource="{Binding ConditionsNodeData.AllFromAndThisNodeDatas}"
                                      SelectedIndex="{Binding SelectedItem.SelectedInputIndex, Mode=TwoWay}"
                                      SelectedItem="{Binding SelectedItem.SelectedInputNodeData}">
                                <b:Interaction.Triggers>
                                    <b:EventTrigger EventName="SelectionChanged">
                                        <b:InvokeCommandAction Command="{Binding SelectionInputChangedCommand}"
                                                               PassEventArgsToCommand="True" />
                                    </b:EventTrigger>
                                </b:Interaction.Triggers>
                            </ComboBox>
                        </BulletDecorator>
                        <BulletDecorator>
                            <BulletDecorator.Bullet>
                                <TextBlock Width="80"
                                           Text=" 目标节点" />
                            </BulletDecorator.Bullet>
                            <ComboBox Height="Auto"
                                      MinWidth="300"
                                      Cattach.ItemHeight="Auto"
                                      Cattach.ItemHorizontalContentAlignment="Left"
                                      ItemsSource="{Binding ConditionsNodeData.ToNodeDatas}"
                                      SelectedIndex="{Binding SelectedItem.SelectedOutputIndex, Mode=TwoWay}"
                                      SelectedItem="{Binding SelectedItem.SelectedOutputNodeData}" />
                        </BulletDecorator>
                    </UniformGrid>
                    <DockPanel Margin="0,1"
                               DockPanel.Dock="Top"
                               LastChildFill="False">

                        <Button Command="{Binding SelectedItem.AddConditionCommand}"
                                Content="添加条件"
                                DockPanel.Dock="Right" />
                        <Button Margin="1,0"
                                Command="{Binding SelectedItem.ClearConditionCommand}"
                                Content="清空条件"
                                DockPanel.Dock="Right" />
                    </DockPanel>
                    <Separator Margin="0,2"
                               DockPanel.Dock="Top"
                               Style="{DynamicResource {x:Static h:SeparatorKeys.Default}}" />
                    <ScrollViewer DataContext="{Binding SelectedItem}"
                                  HorizontalScrollBarVisibility="Disabled"
                                  VerticalScrollBarVisibility="Auto">
                        <ItemsControl ItemsSource="{Binding Conditions}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate DataType="{x:Type PropertyConfidtion}">
                                    <DockPanel>
                                        <CheckBox Margin="2,0,0,0"
                                                  IsChecked="{Binding Filter.IsSelected}" />
                                        <ComboBox Width="120"
                                                  ItemsSource="{Binding RelativeSource={RelativeSource AncestorType=ItemsControl}, Path=DataContext.Properties}"
                                                  SelectedItem="{Binding Filter.PropertyInfo}">
                                            <b:Interaction.Triggers>
                                                <b:EventTrigger EventName="SelectionChanged">
                                                    <b:InvokeCommandAction Command="{Binding SelectionChangedCommand}"
                                                                           PassEventArgsToCommand="True" />
                                                </b:EventTrigger>
                                            </b:Interaction.Triggers>
                                            <ItemsControl.ItemTemplate>
                                                <DataTemplate>
                                                    <TextBlock Text="{Binding ., Converter={x:Static Converter.GetPropertyInfoDiaplayName}}" />
                                                </DataTemplate>
                                            </ItemsControl.ItemTemplate>
                                            <!--<h:Interaction.Triggers>
        <h:EventTrigger EventName="SelectionChanged">
            <h:InvokeCommandAction Command="{Binding SelectionChangedCommand}" PassEventArgsToCommand="True" />
        </h:EventTrigger>
    </h:Interaction.Triggers>-->
                                        </ComboBox>
                                        <FontIconButton BorderBrush="{DynamicResource {x:Static h:BrushKeys.BorderBrush}}"
                                                        BorderThickness="1"
                                                        Cattach.CornerRadius="2"
                                                        Content="{x:Static FontIcons.Cancel}"
                                                        DockPanel.Dock="Right"
                                                        FontSize="12">
                                            <b:Interaction.Behaviors>
                                                <h:ButtonRemoveItemBehavior />
                                            </b:Interaction.Behaviors>
                                        </FontIconButton>
                                        <ContentControl Content="{Binding Filter}" />
                                    </DockPanel>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </ScrollViewer>
                </DockPanel>
            </Border>
        </DockPanel>
    </DataTemplate>
</ResourceDictionary>