﻿// Copyright (c) HeBianGu Authors. All Rights Reserved. 
// Author: HeBianGu 
// Github: https://github.com/HeBianGu/WPF-Control 
// Document: https://hebiangu.github.io/WPF-Control-Docs  
// QQ:908293466 Group:971261058 
// bilibili: https://space.bilibili.com/370266611 
// Licensed under the MIT License (the "License")

namespace H.VisionMaster.OpenCV.NodeDatas.Basic;
[Icon(FontIcons.Color)]
[Display(Name = "通道分割", GroupName = "基础函数", Description = "分割BGR通道,它将彩色图像分解为不同的颜色通道或特征通道，为后续处理提供更精细的数据表示。以下是通道分割的详细作用分析", Order = 30)]
public class SplitBGR : OpenCVNodeDataBase, IPreprocessingGroupableNodeData
{
    private SplitSelectType _splitSelectType;
    [DefaultValue(SplitSelectType.B)]
    [Display(Name = "选择通道", GroupName = VisionPropertyGroupNames.RunParameters)]
    public SplitSelectType SplitSelectType
    {
        get { return _splitSelectType; }
        set
        {
            _splitSelectType = value;
            RaisePropertyChanged();
            this.UpdateInvokeCurrent();
        }
    }

    private bool _useMarge = true;
    [DefaultValue(true)]
    [Display(Name = "启用合并", GroupName = VisionPropertyGroupNames.RunParameters)]
    public bool UseMarge
    {
        get { return _useMarge; }
        set
        {
            _useMarge = value;
            RaisePropertyChanged();
            this.UpdateInvokeCurrent();
        }
    }
    protected override FlowableResult<Mat> Invoke(ISrcVisionNodeData<Mat> srcImageNodeData, IVisionNodeData<Mat> from, IFlowableDiagramData diagram)
    {
        //using var src = new Mat(ImagePath.Lenna, ImreadModes.Color);
        Mat src = from.Mat;
        Cv2.Split(src, out Mat[] mats);

        if (this.UseMarge)
        {
            using Mat zero = new Mat(mats[0].Size(), MatType.CV_8UC1, new Scalar(0));
            if (this.SplitSelectType == SplitSelectType.B)
            {
                Mat sum = new Mat();
                Cv2.Merge(new Mat[] { mats[0], zero, zero }, sum);//(b,0,0)图像
                return this.OK(sum);
            }
            if (this.SplitSelectType == SplitSelectType.G)
            {
                Mat sum = new Mat();
                Cv2.Merge(new Mat[] { zero, mats[1], zero }, sum);//(0,g,0)图像
                return this.OK(sum);
            }
            if (this.SplitSelectType == SplitSelectType.R)
            {
                Mat sum = new Mat();
                Cv2.Merge(new Mat[] { zero, zero, mats[2] }, sum);//(0,0,r)图像
                return this.OK(sum);
            }
        }
        else
        {
            return this.OK(mats[(int)this.SplitSelectType]);
        }

        return this.Error(null);
    }
}

public enum SplitSelectType
{
    B = 0, G, R
}
