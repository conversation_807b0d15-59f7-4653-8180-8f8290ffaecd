﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:b="http://schemas.microsoft.com/xaml/behaviors"
                    xmlns:h="https://github.com/HeBianGu"
                    xmlns:local="clr-namespace:H.App.FileManager">

    <DataTemplate DataType="{x:Type local:FileProjectItem}">
        <DockPanel>
            <Image Width="30"
                   Height="30"
                   Margin="5"
                   Source="{Binding BaseFolder, Converter={h:GetFilePathToSystemInfoIconConverter}}" />
            <UniformGrid Columns="1">
                <Grid>
                    <TextBlock Text="{Binding Title}" />
                    <TextBlock Margin="10,0"
                               HorizontalAlignment="Right"
                               VerticalAlignment="Center"
                               DockPanel.Dock="Right"
                               Text="{Binding UpdateTime, Converter={x:Static h:Converter.GetDateTimeToString}}" />

                </Grid>
                <TextBlock FontSize="{DynamicResource {x:Static h:FontSizeKeys.Header6}}"
                           Foreground="{DynamicResource {x:Static h:BrushKeys.ForegroundAssist}}"
                           Text="{Binding BaseFolder}"
                           TextTrimming="CharacterEllipsis"
                           ToolTip="{Binding BaseFolder}" />
            </UniformGrid>
        </DockPanel>
    </DataTemplate>


</ResourceDictionary>