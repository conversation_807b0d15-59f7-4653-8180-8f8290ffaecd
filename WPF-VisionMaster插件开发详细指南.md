# WPF-VisionMaster 插件开发详细指南

## 🎯 开发环境准备

### 1. 必需软件
- **Visual Studio 2022** (推荐) 或 **Visual Studio Code**
- **.NET 8.0 SDK** 
- **Git** 版本控制工具

### 2. 项目配置验证
```bash
# 验证.NET版本
dotnet --version

# 编译整个解决方案
dotnet build Solution\WPF-VisionMaster.sln --configuration Release
```

## 🏗️ 插件开发步骤详解

### 步骤1: 创建新的节点类文件

**文件位置**: `Source/VisionMaster/H.VisionMaster.OpenCV/NodeDatas/[分组文件夹]/YourNodeData.cs`

**基础模板**:
```csharp
// Copyright (c) HeBianGu Authors. All Rights Reserved.
using H.VisionMaster.NodeGroup.Groups.Preprocessings; // 根据分组选择对应命名空间

namespace H.VisionMaster.OpenCV.NodeDatas.YourCategory;

[Icon(FontIcons.YourIcon)]  // 选择合适的图标
[Display(Name = "节点显示名", GroupName = "分组名", Description = "功能描述", Order = 1)]
public class YourNodeData : OpenCVNodeDataBase, IPreprocessingGroupableNodeData
{
    // 步骤1.1: 定义参数属性
    private double _parameter1 = 0.0;
    [PropertyItem(typeof(DoubleSliderTextPropertyItem))]
    [DefaultValue(0.0)]
    [Range(0.0, 100.0)]
    [Display(Name = "参数1", GroupName = VisionPropertyGroupNames.RunParameters, Description = "参数说明")]
    public double Parameter1
    {
        get { return _parameter1; }
        set
        {
            _parameter1 = value;
            RaisePropertyChanged();
            this.UpdateInvokeCurrent(); // 触发实时更新
        }
    }

    // 步骤1.2: 可选的初始化方法
    public override void LoadDefault()
    {
        base.LoadDefault();
        this.Parameter1 = 10.0; // 设置默认值
    }

    // 步骤1.3: 核心处理逻辑
    protected override FlowableResult<Mat> Invoke(ISrcVisionNodeData<Mat> srcImageNodeData, IVisionNodeData<Mat> from, IFlowableDiagramData diagram)
    {
        // 获取输入图像
        Mat inputMat = from.Mat;
        
        // 验证输入
        if (inputMat == null || inputMat.Empty())
            return this.Error("输入图像为空");

        try
        {
            // 执行图像处理
            Mat outputMat = new Mat();
            // TODO: 在这里添加您的OpenCV处理代码
            inputMat.CopyTo(outputMat);

            return this.OK(outputMat);
        }
        catch (Exception ex)
        {
            return this.Error($"处理失败: {ex.Message}");
        }
    }
}
```

### 步骤2: 选择合适的节点分组

**可用分组接口**:
- `IPreprocessingGroupableNodeData` - 图像预处理
- `IBlurGroupableNodeData` - 模糊滤波
- `IFeatureGroupableNodeData` - 特征提取
- `IMeasurementGroupableNodeData` - 测量分析
- `IClassificationGroupableNodeData` - 分类识别
- `IResultGroupableNodeData` - 结果输出

### 步骤3: 属性配置详解

**常用属性类型**:
```csharp
// 数值滑块
[PropertyItem(typeof(DoubleSliderTextPropertyItem))]
[Range(0.0, 255.0)]
public double ThresholdValue { get; set; }

// 整数输入
[PropertyItem(typeof(IntegerTextPropertyItem))]
public int KernelSize { get; set; }

// 枚举选择
public ThresholdTypes ThresholdType { get; set; }

// 尺寸输入
public System.Windows.Size KernelSize { get; set; }

// 布尔开关
public bool EnableFeature { get; set; }

// 文件路径
[PropertyItem(typeof(FilePathPropertyItem))]
public string ModelPath { get; set; }
```

### 步骤4: 编译和测试

```bash
# 编译项目
dotnet build Source/VisionMaster/H.VisionMaster.OpenCV/H.VisionMaster.OpenCV.csproj

# 编译整个解决方案
dotnet build Solution\WPF-VisionMaster.sln --configuration Release
```

### 步骤5: 调试技巧

**日志记录**:
```csharp
protected override FlowableResult<Mat> Invoke(ISrcVisionNodeData<Mat> srcImageNodeData, IVisionNodeData<Mat> from, IFlowableDiagramData diagram)
{
    // 记录调试信息
    IocLog.Instance?.Info($"开始处理图像，尺寸: {from.Mat.Width}x{from.Mat.Height}");
    
    // 处理逻辑...
    
    IocLog.Instance?.Info("图像处理完成");
    return this.OK(outputMat);
}
```

## 📋 完整示例: 自定义边缘检测节点

```csharp
// 文件: Source/VisionMaster/H.VisionMaster.OpenCV/NodeDatas/2 - Preprocessings/CustomEdgeDetection.cs
using H.VisionMaster.NodeGroup.Groups.Preprocessings;

namespace H.VisionMaster.OpenCV.NodeDatas.Basic;

[Icon(FontIcons.BorderOuter)]
[Display(Name = "自定义边缘检测", GroupName = "边缘检测", Description = "使用Canny算法进行边缘检测", Order = 10)]
public class CustomEdgeDetection : OpenCVNodeDataBase, IPreprocessingGroupableNodeData
{
    private double _lowThreshold = 50.0;
    [PropertyItem(typeof(DoubleSliderTextPropertyItem))]
    [DefaultValue(50.0)]
    [Range(0.0, 255.0)]
    [Display(Name = "低阈值", GroupName = VisionPropertyGroupNames.RunParameters, Description = "Canny边缘检测的低阈值")]
    public double LowThreshold
    {
        get { return _lowThreshold; }
        set
        {
            _lowThreshold = value;
            RaisePropertyChanged();
            this.UpdateInvokeCurrent();
        }
    }

    private double _highThreshold = 150.0;
    [PropertyItem(typeof(DoubleSliderTextPropertyItem))]
    [DefaultValue(150.0)]
    [Range(0.0, 255.0)]
    [Display(Name = "高阈值", GroupName = VisionPropertyGroupNames.RunParameters, Description = "Canny边缘检测的高阈值")]
    public double HighThreshold
    {
        get { return _highThreshold; }
        set
        {
            _highThreshold = value;
            RaisePropertyChanged();
            this.UpdateInvokeCurrent();
        }
    }

    private int _apertureSize = 3;
    [PropertyItem(typeof(IntegerTextPropertyItem))]
    [DefaultValue(3)]
    [Range(3, 7)]
    [Display(Name = "孔径大小", GroupName = VisionPropertyGroupNames.RunParameters, Description = "Sobel算子的孔径大小")]
    public int ApertureSize
    {
        get { return _apertureSize; }
        set
        {
            _apertureSize = value;
            RaisePropertyChanged();
            this.UpdateInvokeCurrent();
        }
    }

    private bool _l2Gradient = false;
    [DefaultValue(false)]
    [Display(Name = "L2梯度", GroupName = VisionPropertyGroupNames.RunParameters, Description = "是否使用L2梯度计算")]
    public bool L2Gradient
    {
        get { return _l2Gradient; }
        set
        {
            _l2Gradient = value;
            RaisePropertyChanged();
            this.UpdateInvokeCurrent();
        }
    }

    public override void LoadDefault()
    {
        base.LoadDefault();
        this.LowThreshold = 50.0;
        this.HighThreshold = 150.0;
        this.ApertureSize = 3;
        this.L2Gradient = false;
    }

    protected override FlowableResult<Mat> Invoke(ISrcVisionNodeData<Mat> srcImageNodeData, IVisionNodeData<Mat> from, IFlowableDiagramData diagram)
    {
        Mat inputMat = from.Mat;
        
        if (inputMat == null || inputMat.Empty())
            return this.Error("输入图像为空");

        try
        {
            // 转换为灰度图像（如果需要）
            Mat grayMat = new Mat();
            if (inputMat.Channels() == 3)
            {
                Cv2.CvtColor(inputMat, grayMat, ColorConversionCodes.BGR2GRAY);
            }
            else
            {
                grayMat = inputMat.Clone();
            }

            // 执行Canny边缘检测
            Mat edgeMat = new Mat();
            Cv2.Canny(grayMat, edgeMat, this.LowThreshold, this.HighThreshold, this.ApertureSize, this.L2Gradient);

            // 清理临时图像
            if (inputMat.Channels() == 3)
                grayMat.Dispose();

            return this.OK(edgeMat, $"边缘检测完成 - 低阈值:{LowThreshold}, 高阈值:{HighThreshold}");
        }
        catch (Exception ex)
        {
            return this.Error($"边缘检测失败: {ex.Message}");
        }
    }
}
```

## 🔧 高级功能开发

### 1. 多输入节点
```csharp
// 获取多个输入
IEnumerable<IVisionNodeData<Mat>> fromNodes = this.GetFromNodeDatas<IVisionNodeData<Mat>>();
foreach (var node in fromNodes)
{
    Mat mat = node.Mat;
    // 处理每个输入...
}
```

### 2. 结果数据展示
```csharp
// 返回带有数据表格的结果
var dataResults = measurements.ToDataGridValueResultPresenter(x => x.ToString(), x => "测量值");
return this.OK(outputMat, dataResults, $"检测到 {count} 个对象");
```

### 3. 异步处理
```csharp
protected override async Task<IFlowableResult> InvokeAsync(IFlowableLinkData from, IFlowableDiagramData diagramData)
{
    return await Task.Run(() =>
    {
        // 长时间运行的处理...
        return this.OK(result);
    });
}
```

## 📝 开发规范

### 命名约定
- 类名: `功能描述` + `NodeData`
- 属性名: 使用PascalCase
- 私有字段: 使用_camelCase

### 错误处理
- 始终验证输入参数
- 使用try-catch包装OpenCV调用
- 提供有意义的错误消息

### 性能优化
- 及时释放Mat对象
- 避免不必要的图像复制
- 使用合适的数据类型

## 🚀 快速开始清单

- [ ] 1. 确认开发环境已配置
- [ ] 2. 选择合适的节点分组
- [ ] 3. 创建节点类文件
- [ ] 4. 实现核心处理逻辑
- [ ] 5. 添加参数属性
- [ ] 6. 编译测试
- [ ] 7. 在应用中验证功能

现在您可以开始创建自己的机器视觉处理节点了！🎉
