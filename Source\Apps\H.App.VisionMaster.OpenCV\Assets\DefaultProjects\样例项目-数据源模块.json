{"$type": "System.Collections.ObjectModel.ObservableCollection`1[[H.VisionMaster.DiagramData.IVisionDiagramData, H.VisionMaster.DiagramData]], System.ObjectModel", "$values": [{"$id": "1", "$type": "H.App.VisionMaster.OpenCV.DiagramDatas.OpenCVVisionDiagramData, H.App.VisionMaster.OpenCV", "UseFlowableSelectToRunning": true, "Name": "图像数据源", "Width": 1000.0, "Height": 1500.0, "Location": "1000,650", "Datas": {"$type": "H.Controls.Diagram.Presenter.DiagramDatas.Base.Datas, H.Controls.Diagram.Presenter", "NodeDatas": {"$type": "System.Collections.Generic.List`1[[H.Controls.Diagram.Datas.INodeData, H.Controls.Diagram]], System.Private.CoreLib", "$values": [{"$id": "2", "$type": "H.VisionMaster.OpenCV.NodeDatas.Src.SrcImageFilesNodeData, H.VisionMaster.OpenCV", "UseAutoSwitch": true, "SrcFilePath": "Assets\\BaseImages\\a01.png", "SrcFilePaths": {"$type": "System.Collections.ObjectModel.ObservableCollection`1[[System.String, System.Private.CoreLib]], System.ObjectModel", "$values": ["Assets\\BaseImages\\a01.png", "Assets\\BaseImages\\alpha1.png", "Assets\\BaseImages\\alpha2.png", "Assets\\BaseImages\\angio-part.png", "Assets\\BaseImages\\atoms.png", "Assets\\BaseImages\\audi2.png", "Assets\\BaseImages\\autobahn.png", "Assets\\BaseImages\\b5_1.png", "Assets\\BaseImages\\bga_14x14_defects.png", "Assets\\BaseImages\\bga_14x14_model.png", "Assets\\BaseImages\\bitrot.tif", "Assets\\BaseImages\\bk45.png", "Assets\\BaseImages\\bottle2.png", "Assets\\BaseImages\\brycecanyon1.png", "Assets\\BaseImages\\cable1.png", "Assets\\BaseImages\\cable2.png", "Assets\\BaseImages\\caltab.png", "Assets\\BaseImages\\can.png", "Assets\\BaseImages\\can_with_grid.png", "Assets\\BaseImages\\circle_plate.png", "Assets\\BaseImages\\circular_barcode.png", "Assets\\BaseImages\\claudia.png", "Assets\\BaseImages\\clip.png", "Assets\\BaseImages\\combine.png", "Assets\\BaseImages\\crystal.png", "Assets\\BaseImages\\die_on_chip.png", "Assets\\BaseImages\\die_pads.png", "Assets\\BaseImages\\dot_print_slanted.png", "Assets\\BaseImages\\double_circle.png", "Assets\\BaseImages\\earth.png", "Assets\\BaseImages\\ed_g.png", "Assets\\BaseImages\\egypt1.png", "Assets\\BaseImages\\engraved.png", "Assets\\BaseImages\\fabrik.png", "Assets\\BaseImages\\fin1.png", "Assets\\BaseImages\\fin2.png", "Assets\\BaseImages\\fin3.png", "Assets\\BaseImages\\fingerprint.png", "Assets\\BaseImages\\for5.png", "Assets\\BaseImages\\for6.png", "Assets\\BaseImages\\forest_air1.png", "Assets\\BaseImages\\forest_road.png", "Assets\\BaseImages\\fuse.png", "Assets\\BaseImages\\glasses_polarized.png", "Assets\\BaseImages\\graph_paper.png", "Assets\\BaseImages\\green-dot.png", "Assets\\BaseImages\\green-dots.png", "Assets\\BaseImages\\horses.png", "Assets\\BaseImages\\hull.png", "Assets\\BaseImages\\ic.png", "Assets\\BaseImages\\ic0.png", "Assets\\BaseImages\\ic1.png", "Assets\\BaseImages\\ic2.png", "Assets\\BaseImages\\ic3.png", "Assets\\BaseImages\\ic_pin.png", "Assets\\BaseImages\\keypad.png", "Assets\\BaseImages\\landmarks.png", "Assets\\BaseImages\\largebw1.tif", "Assets\\BaseImages\\letters.png", "Assets\\BaseImages\\lynx.png", "Assets\\BaseImages\\lynx_mask.png", "Assets\\BaseImages\\marks.png", "Assets\\BaseImages\\meningg5.png", "Assets\\BaseImages\\meningg6.png", "Assets\\BaseImages\\monkey.png", "Assets\\BaseImages\\montery.png", "Assets\\BaseImages\\mreut.png", "Assets\\BaseImages\\mreut4_3.png", "Assets\\BaseImages\\mreut_dgm_2.0.tif", "Assets\\BaseImages\\mreut_hill.png", "Assets\\BaseImages\\mreut_y.png", "Assets\\BaseImages\\multiple_dies_01.png", "Assets\\BaseImages\\multiple_dies_02.png", "Assets\\BaseImages\\mvtec_logo.png", "Assets\\BaseImages\\needle1.png", "Assets\\BaseImages\\numbers_scale.png", "Assets\\BaseImages\\olympic_stadium.png", "Assets\\BaseImages\\pads.png", "Assets\\BaseImages\\particle.png", "Assets\\BaseImages\\patras.png", "Assets\\BaseImages\\pcb.png", "Assets\\BaseImages\\pcb_color.png", "Assets\\BaseImages\\pcb_layout.png", "Assets\\BaseImages\\pellets.png", "Assets\\BaseImages\\pioneer.png", "Assets\\BaseImages\\plan_01.png", "Assets\\BaseImages\\plan_02.png", "Assets\\BaseImages\\plit2.png", "Assets\\BaseImages\\progres.png", "Assets\\BaseImages\\pumpe.png", "Assets\\BaseImages\\punched_holes.png", "Assets\\BaseImages\\razors1.png", "Assets\\BaseImages\\razors2.png", "Assets\\BaseImages\\rim.png", "Assets\\BaseImages\\rings_and_nuts.png", "Assets\\BaseImages\\screw_thread.png", "Assets\\BaseImages\\surface_scratch.png", "Assets\\BaseImages\\tooth_rim.png", "Assets\\BaseImages\\traffic1.png", "Assets\\BaseImages\\traffic2.png", "Assets\\BaseImages\\vessel.png", "Assets\\BaseImages\\woodring.png", "Assets\\BaseImages\\wood_knots.png", "Assets\\BaseImages\\zeiss1.png", "Assets\\board\\board-01.png", "Assets\\board\\board-02.png", "Assets\\board\\board-03.png", "Assets\\board\\board-04.png", "Assets\\board\\board-05.png", "Assets\\board\\board-06.png", "Assets\\board\\board-07.png", "Assets\\board\\board-08.png", "Assets\\board\\board-09.png", "Assets\\board\\board-10.png", "Assets\\board\\board-11.png", "Assets\\board\\board-12.png", "Assets\\board\\board-13.png", "Assets\\board\\board-14.png", "Assets\\board\\board-15.png", "Assets\\board\\board-16.png", "Assets\\board\\board-17.png", "Assets\\board\\board-18.png", "Assets\\board\\board-19.png", "Assets\\board\\board-20.png", "Assets\\board\\board_01.png", "Assets\\board\\board_02.png", "Assets\\board\\board_03.png", "Assets\\board\\board_04.png", "Assets\\board\\board_05.png", "Assets\\board\\board_06.png", "Assets\\board\\board_07.png", "Assets\\board\\board_08.png", "Assets\\board\\board_09.png", "Assets\\bottle_crate\\bottle_crate_01.png", "Assets\\bottle_crate\\bottle_crate_02.png", "Assets\\bottle_crate\\bottle_crate_03.png", "Assets\\bottle_crate\\bottle_crate_04.png", "Assets\\bottle_crate\\bottle_crate_05.png", "Assets\\bottle_crate\\bottle_crate_06.png", "Assets\\bottle_crate\\bottle_crate_07.png", "Assets\\bottle_crate\\bottle_crate_08.png", "Assets\\bottle_crate\\bottle_crate_09.png", "Assets\\bottle_crate\\bottle_crate_10.png", "Assets\\bottle_crate\\bottle_crate_11.png", "Assets\\bottle_crate\\bottle_crate_12.png", "Assets\\bottle_crate\\bottle_crate_13.png", "Assets\\bottle_crate\\bottle_crate_14.png", "Assets\\bottle_crate\\bottle_crate_15.png", "Assets\\bottle_crate\\bottle_crate_16.png", "Assets\\bottle_crate\\bottle_crate_17.png", "Assets\\bottle_crate\\bottle_crate_18.png", "Assets\\bottle_crate\\bottle_crate_19.png", "Assets\\bottle_crate\\bottle_crate_20.png", "Assets\\bottle_crate\\bottle_crate_21.png", "Assets\\bottle_crate\\bottle_crate_22.png", "Assets\\bottle_crate\\bottle_crate_23.png", "Assets\\bottle_crate\\bottle_crate_24.png", "Assets\\box.bmp", "Assets\\car_door\\car_door_01.png", "Assets\\car_door\\car_door_02.png", "Assets\\car_door\\car_door_03.png", "Assets\\car_door\\car_door_04.png", "Assets\\car_door\\car_door_05.png", "Assets\\car_door\\car_door_06.png", "Assets\\car_door\\car_door_07.png", "Assets\\car_door\\car_door_08.png", "Assets\\car_door\\car_door_09.png", "Assets\\car_door\\car_door_10.png", "Assets\\car_door\\car_door_11.png", "Assets\\car_door\\car_door_12.png", "Assets\\car_door\\car_door_13.png", "Assets\\car_door\\car_door_14.png", "Assets\\car_door\\car_door_15.png", "Assets\\car_door\\car_door_16.png", "Assets\\car_door\\car_door_17.png", "Assets\\car_door\\car_door_18.png", "Assets\\car_door\\car_door_19.png", "Assets\\car_door\\car_door_20.png", "Assets\\car_door\\car_door_21.png", "Assets\\car_door\\car_door_calib_plate.png", "Assets\\car_door\\car_door_init_pose.png", "Assets\\green.jpg", "Assets\\multi_view_bitholder_cam\\multi_view_bitholder_cam_0_1.png", "Assets\\multi_view_bitholder_cam\\multi_view_bitholder_cam_0_10.png", "Assets\\multi_view_bitholder_cam\\multi_view_bitholder_cam_0_2.png", "Assets\\multi_view_bitholder_cam\\multi_view_bitholder_cam_0_3.png", "Assets\\multi_view_bitholder_cam\\multi_view_bitholder_cam_0_4.png", "Assets\\multi_view_bitholder_cam\\multi_view_bitholder_cam_0_7.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_01.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_02.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_03.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_04.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_05.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_06.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_07.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_08.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_09.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_10.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_11.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_12.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_13.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_14.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_15.png", "Assets\\OpenCV\\00.JPG", "Assets\\OpenCV\\01.JPG", "Assets\\OpenCV\\02.JPG", "Assets\\OpenCV\\16bit.png", "Assets\\OpenCV\\aruco_markers_photo.jpg", "Assets\\OpenCV\\aruco_markers_source.jpg", "Assets\\OpenCV\\asahiyama.jpg", "Assets\\OpenCV\\Balloon.png", "Assets\\OpenCV\\binarization_sample.bmp", "Assets\\OpenCV\\box.png", "Assets\\OpenCV\\box_in_scene.png", "Assets\\OpenCV\\cake.bmp", "Assets\\OpenCV\\cat.jpg", "Assets\\OpenCV\\circle.png", "Assets\\OpenCV\\cvmorph.png", "Assets\\OpenCV\\fruits.jpg", "Assets\\OpenCV\\Girl.bmp", "Assets\\OpenCV\\goryokaku.jpg", "Assets\\OpenCV\\hand_p.jpg", "Assets\\OpenCV\\left01.jpg", "Assets\\OpenCV\\left02.jpg", "Assets\\OpenCV\\left03.jpg", "Assets\\OpenCV\\left04.jpg", "Assets\\OpenCV\\left05.jpg", "Assets\\OpenCV\\left06.jpg", "Assets\\OpenCV\\left07.jpg", "Assets\\OpenCV\\left08.jpg", "Assets\\OpenCV\\left09.jpg", "Assets\\OpenCV\\left10.jpg", "Assets\\OpenCV\\left11.jpg", "Assets\\OpenCV\\left12.jpg", "Assets\\OpenCV\\left13.jpg", "Assets\\OpenCV\\lenna.png", "Assets\\OpenCV\\lenna511.png", "Assets\\OpenCV\\maltese.jpg", "Assets\\OpenCV\\Mandrill.bmp", "Assets\\OpenCV\\match1.png", "Assets\\OpenCV\\match2.png", "Assets\\OpenCV\\penguin1.png", "Assets\\OpenCV\\penguin1b.png", "Assets\\OpenCV\\penguin2.png", "Assets\\OpenCV\\pentagon.png", "Assets\\OpenCV\\pic1.png", "Assets\\OpenCV\\pic2.png", "Assets\\OpenCV\\pic3.png", "Assets\\OpenCV\\pic4.png", "Assets\\OpenCV\\pic5.png", "Assets\\OpenCV\\pic6.png", "Assets\\OpenCV\\right01.jpg", "Assets\\OpenCV\\right02.jpg", "Assets\\OpenCV\\right03.jpg", "Assets\\OpenCV\\right04.jpg", "Assets\\OpenCV\\right05.jpg", "Assets\\OpenCV\\right06.jpg", "Assets\\OpenCV\\right07.jpg", "Assets\\OpenCV\\right08.jpg", "Assets\\OpenCV\\right09.jpg", "Assets\\OpenCV\\right10.jpg", "Assets\\OpenCV\\right11.jpg", "Assets\\OpenCV\\right12.jpg", "Assets\\OpenCV\\right13.jpg", "Assets\\OpenCV\\shapes.png", "Assets\\OpenCV\\space_shuttle.jpg", "Assets\\OpenCV\\tsukuba_left.png", "Assets\\OpenCV\\tsukuba_right.png", "Assets\\OpenCV\\very_old_newspaper.png", "Assets\\OpenCV\\walkman.jpg", "Assets\\OpenCV\\yalta.jpg", "Assets\\Person\\009445.jpg", "Assets\\Person\\009453.jpg", "Assets\\Person\\009456.jpg", "Assets\\Person\\1.jpg", "Assets\\Person\\Image1.jpg", "Assets\\Person\\Image4.jpg", "Assets\\Person\\Image5.jpg", "Assets\\Person\\woman.jpg", "Assets\\Person\\yolov6-inference-cycling.jpg", "Assets\\Person\\yolov6-inference-soccer.jpg", "Assets\\pill_bag\\pill_bag_001.png", "Assets\\pill_bag\\pill_bag_002.png", "Assets\\pill_bag\\pill_bag_003.png", "Assets\\pill_bag\\pill_bag_004.png", "Assets\\pill_bag\\pill_bag_005.png", "Assets\\pill_bag\\pill_bag_006.png", "Assets\\pill_bag\\pill_bag_007.png", "Assets\\pill_bag\\pill_bag_008.png", "Assets\\pill_bag\\pill_bag_009.png", "Assets\\pill_bag\\pill_bag_010.png", "Assets\\pill_magnesium_crack\\pill_magnesium_crack_280.png", "Assets\\pill_magnesium_crack\\pill_magnesium_crack_281.png", "Assets\\pill_magnesium_crack\\pill_magnesium_crack_282.png", "Assets\\pill_magnesium_crack\\pill_magnesium_crack_283.png", "Assets\\pill_magnesium_crack\\pill_magnesium_crack_284.png", "Assets\\pill_magnesium_crack\\pill_magnesium_crack_285.png", "Assets\\pill_magnesium_crack\\pill_magnesium_crack_286.png", "Assets\\pill_magnesium_crack\\pill_magnesium_crack_287.png", "Assets\\pill_magnesium_crack\\pill_magnesium_crack_288.png", "Assets\\pill_magnesium_crack\\pill_magnesium_crack_289.png", "Assets\\radius-gauges\\radius-gauges-00.png", "Assets\\radius-gauges\\radius-gauges-01.png", "Assets\\radius-gauges\\radius-gauges-02.png", "Assets\\radius-gauges\\radius-gauges-03.png", "Assets\\radius-gauges\\radius-gauges-04.png", "Assets\\radius-gauges\\radius-gauges-05.png", "Assets\\radius-gauges\\radius-gauges-06.png", "Assets\\radius-gauges\\radius-gauges-07.png", "Assets\\radius-gauges\\radius-gauges-08.png", "Assets\\radius-gauges\\radius-gauges-09.png", "Assets\\radius-gauges\\radius-gauges-10.png", "Assets\\radius-gauges\\radius-gauges-11.png", "Assets\\radius-gauges\\radius-gauges-12.png", "Assets\\radius-gauges\\radius-gauges-13.png", "Assets\\radius-gauges\\radius-gauges-14.png", "Assets\\radius-gauges\\radius-gauges-15.png", "Assets\\radius-gauges\\radius-gauges-16.png", "Assets\\radius-gauges\\radius-gauges-17.png", "Assets\\radius-gauges\\radius-gauges-18.png", "Assets\\radius-gauges\\radius-gauges-19.png", "Assets\\radius-gauges\\radius-gauges-20.png", "Assets\\rice.png", "Assets\\zhifang_ball.png"]}, "ROI": {"$id": "3", "$type": "H.VisionMaster.NodeData.ROIPresenters.FromROI, H.VisionMaster.NodeData", "ID": "30d94b98-32ea-4986-9e6f-84975c7890a4", "Name": "继承"}, "FromROI": {"$ref": "3"}, "DrawROI": {"$id": "4", "$type": "H.VisionMaster.NodeData.ROIPresenters.DrawROI, H.VisionMaster.NodeData", "Rect": "Empty", "ID": "3f08c573-a86a-460a-a5f9-1f66829d15a0", "Name": "绘制"}, "InputROI": {"$id": "5", "$type": "H.VisionMaster.NodeData.ROIPresenters.InputROI, H.VisionMaster.NodeData", "ID": "44b05b0e-ee6c-4193-b18a-9af6c91a431f", "Name": "输入"}, "PreviewMillisecondsDelay": 0, "InvokeMillisecondsDelay": 0, "FlagLength": 10.0, "DiagramData": {"$ref": "1"}, "Text": "本地图像源", "FontSize": 12.0, "PortDatas": {"$type": "System.Collections.Generic.List`1[[H.Controls.Diagram.Datas.IPortData, H.Controls.Diagram]], System.Private.CoreLib", "$values": [{"$id": "6", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Top", "NodeID": "a47b58b8-1bfb-466b-b1a4-1ff892a6a162", "PortType": "Input", "ID": "a4ac3518-3b93-40d7-8490-8fe171d392c4"}, {"$id": "7", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Bottom", "NodeID": "a47b58b8-1bfb-466b-b1a4-1ff892a6a162", "PortType": "OutPut", "ID": "39576711-db80-4e0a-974a-3067579bad3a"}, {"$id": "8", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "NodeID": "a47b58b8-1bfb-466b-b1a4-1ff892a6a162", "PortType": "Input", "ID": "80588b9f-0c7b-48ac-bb56-8ba57caaf61d"}, {"$id": "9", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Right", "NodeID": "a47b58b8-1bfb-466b-b1a4-1ff892a6a162", "PortType": "OutPut", "ID": "2961ad0e-4671-43d7-be2a-536dd258ae8f"}]}, "IsTemplate": false, "Height": 35.0, "Width": 120.0, "Fill": "#FFFFFFFF", "CornerRadius": 2.0, "Location": "380.16296296296287,738.5851851851851", "ID": "a47b58b8-1bfb-466b-b1a4-1ff892a6a162", "Name": "本地图像源", "Icon": ""}, {"$id": "10", "$type": "H.App.VisionMaster.OpenCV.NodeDatas.SrcImages.OpenCVBitholderSrcImageFilesNodeData, H.App.VisionMaster.OpenCV", "UseAutoSwitch": true, "SrcFilePath": "Assets\\multi_view_bitholder_cam\\multi_view_bitholder_cam_0_1.png", "SrcFilePaths": {"$type": "System.Collections.ObjectModel.ObservableCollection`1[[System.String, System.Private.CoreLib]], System.ObjectModel", "$values": ["Assets\\multi_view_bitholder_cam\\multi_view_bitholder_cam_0_1.png", "Assets\\multi_view_bitholder_cam\\multi_view_bitholder_cam_0_10.png", "Assets\\multi_view_bitholder_cam\\multi_view_bitholder_cam_0_2.png", "Assets\\multi_view_bitholder_cam\\multi_view_bitholder_cam_0_3.png", "Assets\\multi_view_bitholder_cam\\multi_view_bitholder_cam_0_4.png", "Assets\\multi_view_bitholder_cam\\multi_view_bitholder_cam_0_7.png"]}, "ROI": {"$id": "11", "$type": "H.VisionMaster.NodeData.ROIPresenters.FromROI, H.VisionMaster.NodeData", "ID": "8c65af60-c78e-4bdb-8e1d-47604e7a9987", "Name": "继承"}, "FromROI": {"$ref": "11"}, "DrawROI": {"$id": "12", "$type": "H.VisionMaster.NodeData.ROIPresenters.DrawROI, H.VisionMaster.NodeData", "Rect": "Empty", "ID": "bd9e1eaf-9f28-409c-8b20-bf67e0b58eb4", "Name": "绘制"}, "InputROI": {"$id": "13", "$type": "H.VisionMaster.NodeData.ROIPresenters.InputROI, H.VisionMaster.NodeData", "ID": "78e92914-f8f6-45d2-9c0e-6ac2c6841abb", "Name": "输入"}, "PreviewMillisecondsDelay": 0, "InvokeMillisecondsDelay": 0, "FlagLength": 10.0, "DiagramData": {"$ref": "1"}, "Text": "夹具数据源", "FontSize": 12.0, "PortDatas": {"$type": "System.Collections.Generic.List`1[[H.Controls.Diagram.Datas.IPortData, H.Controls.Diagram]], System.Private.CoreLib", "$values": [{"$id": "14", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Top", "NodeID": "b3500e99-cd63-452c-9252-581c907d18af", "PortType": "Input", "ID": "8c2581d4-52ff-4315-8874-e5936a557d02"}, {"$id": "15", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Bottom", "NodeID": "b3500e99-cd63-452c-9252-581c907d18af", "PortType": "OutPut", "ID": "0cf4a559-0e52-4ed0-8199-17bd9038225b"}, {"$id": "16", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "NodeID": "b3500e99-cd63-452c-9252-581c907d18af", "PortType": "Input", "ID": "6cc92402-bfc9-4fca-b5b7-35a1fe284d40"}, {"$id": "17", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Right", "NodeID": "b3500e99-cd63-452c-9252-581c907d18af", "PortType": "OutPut", "ID": "19ba47ba-fb35-4571-951a-cef8010c3c87"}]}, "IsTemplate": false, "Height": 35.0, "Width": 120.0, "Fill": "#FFFFFFFF", "CornerRadius": 2.0, "Location": "554.1629629629629,738.5851851851851", "ID": "b3500e99-cd63-452c-9252-581c907d18af", "Name": "夹具数据源", "Icon": ""}, {"$id": "18", "$type": "H.App.VisionMaster.OpenCV.NodeDatas.SrcImages.OpenCVBoardSrcImageFilesNodeData, H.App.VisionMaster.OpenCV", "UseAutoSwitch": true, "SrcFilePath": "Assets\\board\\board-01.png", "SrcFilePaths": {"$type": "System.Collections.ObjectModel.ObservableCollection`1[[System.String, System.Private.CoreLib]], System.ObjectModel", "$values": ["Assets\\board\\board-01.png", "Assets\\board\\board-02.png", "Assets\\board\\board-03.png", "Assets\\board\\board-04.png", "Assets\\board\\board-05.png", "Assets\\board\\board-06.png", "Assets\\board\\board-07.png", "Assets\\board\\board-08.png", "Assets\\board\\board-09.png", "Assets\\board\\board-10.png", "Assets\\board\\board-11.png", "Assets\\board\\board-12.png", "Assets\\board\\board-13.png", "Assets\\board\\board-14.png", "Assets\\board\\board-15.png", "Assets\\board\\board-16.png", "Assets\\board\\board-17.png", "Assets\\board\\board-18.png", "Assets\\board\\board-19.png", "Assets\\board\\board-20.png", "Assets\\board\\board_01.png", "Assets\\board\\board_02.png", "Assets\\board\\board_03.png", "Assets\\board\\board_04.png", "Assets\\board\\board_05.png", "Assets\\board\\board_06.png", "Assets\\board\\board_07.png", "Assets\\board\\board_08.png", "Assets\\board\\board_09.png"]}, "ROI": {"$id": "19", "$type": "H.VisionMaster.NodeData.ROIPresenters.FromROI, H.VisionMaster.NodeData", "ID": "2fceca06-2567-4b79-9a06-6047ad5105c7", "Name": "继承"}, "FromROI": {"$ref": "19"}, "DrawROI": {"$id": "20", "$type": "H.VisionMaster.NodeData.ROIPresenters.DrawROI, H.VisionMaster.NodeData", "Rect": "Empty", "ID": "2567989c-9047-4f5e-bc47-33cd2201ec99", "Name": "绘制"}, "InputROI": {"$id": "21", "$type": "H.VisionMaster.NodeData.ROIPresenters.InputROI, H.VisionMaster.NodeData", "ID": "0d5e3cef-4f26-4091-a5d7-d9298899236d", "Name": "输入"}, "PreviewMillisecondsDelay": 0, "InvokeMillisecondsDelay": 0, "FlagLength": 10.0, "DiagramData": {"$ref": "1"}, "Text": "芯片图像源", "FontSize": 12.0, "PortDatas": {"$type": "System.Collections.Generic.List`1[[H.Controls.Diagram.Datas.IPortData, H.Controls.Diagram]], System.Private.CoreLib", "$values": [{"$id": "22", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Top", "NodeID": "0cba1e1e-2fe4-40ff-8ce5-d3b20b72c904", "PortType": "Input", "ID": "2a39b832-d57a-4293-81c7-6117044d347d"}, {"$id": "23", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Bottom", "NodeID": "0cba1e1e-2fe4-40ff-8ce5-d3b20b72c904", "PortType": "OutPut", "ID": "15077503-73d6-465f-9ecb-10023779d018"}, {"$id": "24", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "NodeID": "0cba1e1e-2fe4-40ff-8ce5-d3b20b72c904", "PortType": "Input", "ID": "f3c328a7-f326-476e-9cbd-e712b2201682"}, {"$id": "25", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Right", "NodeID": "0cba1e1e-2fe4-40ff-8ce5-d3b20b72c904", "PortType": "OutPut", "ID": "ef15509a-d70d-4247-8c19-feb43fe31f86"}]}, "IsTemplate": false, "Height": 35.0, "Width": 120.0, "Fill": "#FFFFFFFF", "CornerRadius": 2.0, "Location": "554.1629629629629,518.5851851851851", "ID": "0cba1e1e-2fe4-40ff-8ce5-d3b20b72c904", "Name": "芯片图像源", "Icon": ""}, {"$id": "26", "$type": "H.App.VisionMaster.OpenCV.NodeDatas.SrcImages.OpenCVCardoorSrcImageFilesNodeData, H.App.VisionMaster.OpenCV", "UseAutoSwitch": true, "SrcFilePath": "Assets\\car_door\\car_door_01.png", "SrcFilePaths": {"$type": "System.Collections.ObjectModel.ObservableCollection`1[[System.String, System.Private.CoreLib]], System.ObjectModel", "$values": ["Assets\\car_door\\car_door_01.png", "Assets\\car_door\\car_door_02.png", "Assets\\car_door\\car_door_03.png", "Assets\\car_door\\car_door_04.png", "Assets\\car_door\\car_door_05.png", "Assets\\car_door\\car_door_06.png", "Assets\\car_door\\car_door_07.png", "Assets\\car_door\\car_door_08.png", "Assets\\car_door\\car_door_09.png", "Assets\\car_door\\car_door_10.png", "Assets\\car_door\\car_door_11.png", "Assets\\car_door\\car_door_12.png", "Assets\\car_door\\car_door_13.png", "Assets\\car_door\\car_door_14.png", "Assets\\car_door\\car_door_15.png", "Assets\\car_door\\car_door_16.png", "Assets\\car_door\\car_door_17.png", "Assets\\car_door\\car_door_18.png", "Assets\\car_door\\car_door_19.png", "Assets\\car_door\\car_door_20.png", "Assets\\car_door\\car_door_21.png", "Assets\\car_door\\car_door_calib_plate.png", "Assets\\car_door\\car_door_init_pose.png"]}, "ROI": {"$id": "27", "$type": "H.VisionMaster.NodeData.ROIPresenters.FromROI, H.VisionMaster.NodeData", "ID": "c49fa5f4-b653-45cf-8c55-574c0c904384", "Name": "继承"}, "FromROI": {"$ref": "27"}, "DrawROI": {"$id": "28", "$type": "H.VisionMaster.NodeData.ROIPresenters.DrawROI, H.VisionMaster.NodeData", "Rect": "Empty", "ID": "a2ffd9f4-e3dc-4a7d-858a-66bb5f59b97b", "Name": "绘制"}, "InputROI": {"$id": "29", "$type": "H.VisionMaster.NodeData.ROIPresenters.InputROI, H.VisionMaster.NodeData", "ID": "65c56b6f-c175-4e13-8ba6-95d22e0f6d56", "Name": "输入"}, "PreviewMillisecondsDelay": 0, "InvokeMillisecondsDelay": 0, "FlagLength": 10.0, "DiagramData": {"$ref": "1"}, "Text": "车门图像源", "FontSize": 12.0, "PortDatas": {"$type": "System.Collections.Generic.List`1[[H.Controls.Diagram.Datas.IPortData, H.Controls.Diagram]], System.Private.CoreLib", "$values": [{"$id": "30", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Top", "NodeID": "b226a5e1-aacc-41fc-8926-37133d3374f6", "PortType": "Input", "ID": "074b36e2-25de-436e-9276-ddd513129dbd"}, {"$id": "31", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Bottom", "NodeID": "b226a5e1-aacc-41fc-8926-37133d3374f6", "PortType": "OutPut", "ID": "d98a2427-e164-4acd-81ef-07447d36550b"}, {"$id": "32", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "NodeID": "b226a5e1-aacc-41fc-8926-37133d3374f6", "PortType": "Input", "ID": "0b8cdf23-2fda-4b90-8ae8-18b6c77e122a"}, {"$id": "33", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Right", "NodeID": "b226a5e1-aacc-41fc-8926-37133d3374f6", "PortType": "OutPut", "ID": "23a0ba7d-0823-434f-ac69-66a8bcca11b0"}]}, "IsTemplate": false, "Height": 35.0, "Width": 120.0, "Fill": "#FFFFFFFF", "CornerRadius": 2.0, "Location": "554.1629629629629,563.5851851851851", "ID": "b226a5e1-aacc-41fc-8926-37133d3374f6", "Name": "车门图像源", "Icon": ""}, {"$id": "34", "$type": "H.App.VisionMaster.OpenCV.NodeDatas.SrcImages.OpenCVHalconSrcImageFilesNodeData, H.App.VisionMaster.OpenCV", "UseAutoSwitch": true, "SrcFilePath": "Assets\\BaseImages\\a01.png", "SrcFilePaths": {"$type": "System.Collections.ObjectModel.ObservableCollection`1[[System.String, System.Private.CoreLib]], System.ObjectModel", "$values": ["Assets\\BaseImages\\a01.png", "Assets\\BaseImages\\alpha1.png", "Assets\\BaseImages\\alpha2.png", "Assets\\BaseImages\\angio-part.png", "Assets\\BaseImages\\atoms.png", "Assets\\BaseImages\\audi2.png", "Assets\\BaseImages\\autobahn.png", "Assets\\BaseImages\\b5_1.png", "Assets\\BaseImages\\bga_14x14_defects.png", "Assets\\BaseImages\\bga_14x14_model.png", "Assets\\BaseImages\\bitrot.tif", "Assets\\BaseImages\\bk45.png", "Assets\\BaseImages\\bottle2.png", "Assets\\BaseImages\\brycecanyon1.png", "Assets\\BaseImages\\cable1.png", "Assets\\BaseImages\\cable2.png", "Assets\\BaseImages\\caltab.png", "Assets\\BaseImages\\can.png", "Assets\\BaseImages\\can_with_grid.png", "Assets\\BaseImages\\circle_plate.png", "Assets\\BaseImages\\circular_barcode.png", "Assets\\BaseImages\\claudia.png", "Assets\\BaseImages\\clip.png", "Assets\\BaseImages\\combine.png", "Assets\\BaseImages\\crystal.png", "Assets\\BaseImages\\die_on_chip.png", "Assets\\BaseImages\\die_pads.png", "Assets\\BaseImages\\dot_print_slanted.png", "Assets\\BaseImages\\double_circle.png", "Assets\\BaseImages\\earth.png", "Assets\\BaseImages\\ed_g.png", "Assets\\BaseImages\\egypt1.png", "Assets\\BaseImages\\engraved.png", "Assets\\BaseImages\\fabrik.png", "Assets\\BaseImages\\fin1.png", "Assets\\BaseImages\\fin2.png", "Assets\\BaseImages\\fin3.png", "Assets\\BaseImages\\fingerprint.png", "Assets\\BaseImages\\for5.png", "Assets\\BaseImages\\for6.png", "Assets\\BaseImages\\forest_air1.png", "Assets\\BaseImages\\forest_road.png", "Assets\\BaseImages\\fuse.png", "Assets\\BaseImages\\glasses_polarized.png", "Assets\\BaseImages\\graph_paper.png", "Assets\\BaseImages\\green-dot.png", "Assets\\BaseImages\\green-dots.png", "Assets\\BaseImages\\horses.png", "Assets\\BaseImages\\hull.png", "Assets\\BaseImages\\ic.png", "Assets\\BaseImages\\ic0.png", "Assets\\BaseImages\\ic1.png", "Assets\\BaseImages\\ic2.png", "Assets\\BaseImages\\ic3.png", "Assets\\BaseImages\\ic_pin.png", "Assets\\BaseImages\\keypad.png", "Assets\\BaseImages\\landmarks.png", "Assets\\BaseImages\\largebw1.tif", "Assets\\BaseImages\\letters.png", "Assets\\BaseImages\\lynx.png", "Assets\\BaseImages\\lynx_mask.png", "Assets\\BaseImages\\marks.png", "Assets\\BaseImages\\meningg5.png", "Assets\\BaseImages\\meningg6.png", "Assets\\BaseImages\\monkey.png", "Assets\\BaseImages\\montery.png", "Assets\\BaseImages\\mreut.png", "Assets\\BaseImages\\mreut4_3.png", "Assets\\BaseImages\\mreut_dgm_2.0.tif", "Assets\\BaseImages\\mreut_hill.png", "Assets\\BaseImages\\mreut_y.png", "Assets\\BaseImages\\multiple_dies_01.png", "Assets\\BaseImages\\multiple_dies_02.png", "Assets\\BaseImages\\mvtec_logo.png", "Assets\\BaseImages\\needle1.png", "Assets\\BaseImages\\numbers_scale.png", "Assets\\BaseImages\\olympic_stadium.png", "Assets\\BaseImages\\pads.png", "Assets\\BaseImages\\particle.png", "Assets\\BaseImages\\patras.png", "Assets\\BaseImages\\pcb.png", "Assets\\BaseImages\\pcb_color.png", "Assets\\BaseImages\\pcb_layout.png", "Assets\\BaseImages\\pellets.png", "Assets\\BaseImages\\pioneer.png", "Assets\\BaseImages\\plan_01.png", "Assets\\BaseImages\\plan_02.png", "Assets\\BaseImages\\plit2.png", "Assets\\BaseImages\\progres.png", "Assets\\BaseImages\\pumpe.png", "Assets\\BaseImages\\punched_holes.png", "Assets\\BaseImages\\razors1.png", "Assets\\BaseImages\\razors2.png", "Assets\\BaseImages\\rim.png", "Assets\\BaseImages\\rings_and_nuts.png", "Assets\\BaseImages\\screw_thread.png", "Assets\\BaseImages\\surface_scratch.png", "Assets\\BaseImages\\tooth_rim.png", "Assets\\BaseImages\\traffic1.png", "Assets\\BaseImages\\traffic2.png", "Assets\\BaseImages\\vessel.png", "Assets\\BaseImages\\woodring.png", "Assets\\BaseImages\\wood_knots.png", "Assets\\BaseImages\\zeiss1.png"]}, "ROI": {"$id": "35", "$type": "H.VisionMaster.NodeData.ROIPresenters.FromROI, H.VisionMaster.NodeData", "ID": "7edef53b-7d03-4ad2-9c74-a7befab9ab37", "Name": "继承"}, "FromROI": {"$ref": "35"}, "DrawROI": {"$id": "36", "$type": "H.VisionMaster.NodeData.ROIPresenters.DrawROI, H.VisionMaster.NodeData", "Rect": "Empty", "ID": "f1e01229-5a52-4201-9eb9-9e5bd6f2e5d5", "Name": "绘制"}, "InputROI": {"$id": "37", "$type": "H.VisionMaster.NodeData.ROIPresenters.InputROI, H.VisionMaster.NodeData", "ID": "0243e53d-24c0-484d-9fc6-c907c3e81f13", "Name": "输入"}, "PreviewMillisecondsDelay": 0, "InvokeMillisecondsDelay": 0, "FlagLength": 10.0, "DiagramData": {"$ref": "1"}, "Text": "Halcon图像源", "FontSize": 12.0, "PortDatas": {"$type": "System.Collections.Generic.List`1[[H.Controls.Diagram.Datas.IPortData, H.Controls.Diagram]], System.Private.CoreLib", "$values": [{"$id": "38", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Top", "NodeID": "6cc4957d-5bb7-4949-9f79-bd3d0d6a44c5", "PortType": "Input", "ID": "338737e6-882c-420f-85db-8a247ebbe338"}, {"$id": "39", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Bottom", "NodeID": "6cc4957d-5bb7-4949-9f79-bd3d0d6a44c5", "PortType": "OutPut", "ID": "45a883de-3322-428a-a0ba-50507b1abdc8"}, {"$id": "40", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "NodeID": "6cc4957d-5bb7-4949-9f79-bd3d0d6a44c5", "PortType": "Input", "ID": "7d407fe7-58fe-4c12-8066-af444db4fc91"}, {"$id": "41", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Right", "NodeID": "6cc4957d-5bb7-4949-9f79-bd3d0d6a44c5", "PortType": "OutPut", "ID": "87e8758d-b381-4aa8-b747-e38cae572106"}]}, "IsTemplate": false, "Height": 35.0, "Width": 120.0, "Fill": "#FFFFFFFF", "CornerRadius": 2.0, "Location": "554.1629629629629,608.5851851851851", "ID": "6cc4957d-5bb7-4949-9f79-bd3d0d6a44c5", "Name": "Halcon图像源", "Icon": ""}, {"$id": "42", "$type": "H.App.VisionMaster.OpenCV.NodeDatas.SrcImages.OpenCVPillbagSrcImageFilesNodeData, H.App.VisionMaster.OpenCV", "UseAutoSwitch": true, "SrcFilePath": "Assets\\pill_bag\\pill_bag_001.png", "SrcFilePaths": {"$type": "System.Collections.ObjectModel.ObservableCollection`1[[System.String, System.Private.CoreLib]], System.ObjectModel", "$values": ["Assets\\pill_bag\\pill_bag_001.png", "Assets\\pill_bag\\pill_bag_002.png", "Assets\\pill_bag\\pill_bag_003.png", "Assets\\pill_bag\\pill_bag_004.png", "Assets\\pill_bag\\pill_bag_005.png", "Assets\\pill_bag\\pill_bag_006.png", "Assets\\pill_bag\\pill_bag_007.png", "Assets\\pill_bag\\pill_bag_008.png", "Assets\\pill_bag\\pill_bag_009.png", "Assets\\pill_bag\\pill_bag_010.png"]}, "ROI": {"$id": "43", "$type": "H.VisionMaster.NodeData.ROIPresenters.FromROI, H.VisionMaster.NodeData", "ID": "58959bec-75ce-45d7-b43b-9eb0738223c3", "Name": "继承"}, "FromROI": {"$ref": "43"}, "DrawROI": {"$id": "44", "$type": "H.VisionMaster.NodeData.ROIPresenters.DrawROI, H.VisionMaster.NodeData", "Rect": "Empty", "ID": "ac90a32d-d9ef-4777-b549-7cd5aa4cf2dc", "Name": "绘制"}, "InputROI": {"$id": "45", "$type": "H.VisionMaster.NodeData.ROIPresenters.InputROI, H.VisionMaster.NodeData", "ID": "9bfee271-33eb-4ec9-8f0f-99506ea6a31f", "Name": "输入"}, "PreviewMillisecondsDelay": 0, "InvokeMillisecondsDelay": 0, "FlagLength": 10.0, "DiagramData": {"$ref": "1"}, "Text": "药丸袋图像源", "FontSize": 12.0, "PortDatas": {"$type": "System.Collections.Generic.List`1[[H.Controls.Diagram.Datas.IPortData, H.Controls.Diagram]], System.Private.CoreLib", "$values": [{"$id": "46", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Top", "NodeID": "efd08586-252a-4a3c-8b50-1438035310f4", "PortType": "Input", "ID": "a90cb9de-4d46-4c1a-b1ba-a39b1635ad96"}, {"$id": "47", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Bottom", "NodeID": "efd08586-252a-4a3c-8b50-1438035310f4", "PortType": "OutPut", "ID": "ef03c1dd-c61c-43f1-909b-82f5248b8292"}, {"$id": "48", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "NodeID": "efd08586-252a-4a3c-8b50-1438035310f4", "PortType": "Input", "ID": "bfe7f009-51ce-4f7d-ab57-e3bb029512b5"}, {"$id": "49", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Right", "NodeID": "efd08586-252a-4a3c-8b50-1438035310f4", "PortType": "OutPut", "ID": "355bc248-bbf1-4aac-866a-eb6ccf2c42c3"}]}, "IsTemplate": false, "Height": 35.0, "Width": 120.0, "Fill": "#FFFFFFFF", "CornerRadius": 2.0, "Location": "553.5541182595002,698.5851851851851", "ID": "efd08586-252a-4a3c-8b50-1438035310f4", "Name": "药丸袋图像源", "Icon": ""}, {"$id": "50", "$type": "H.App.VisionMaster.OpenCV.NodeDatas.SrcImages.OpenCVPillMagnesiumSrcImageFilesNodeData, H.App.VisionMaster.OpenCV", "UseAutoSwitch": true, "SrcFilePath": "Assets\\pill_magnesium_crack\\pill_magnesium_crack_280.png", "SrcFilePaths": {"$type": "System.Collections.ObjectModel.ObservableCollection`1[[System.String, System.Private.CoreLib]], System.ObjectModel", "$values": ["Assets\\pill_magnesium_crack\\pill_magnesium_crack_280.png", "Assets\\pill_magnesium_crack\\pill_magnesium_crack_281.png", "Assets\\pill_magnesium_crack\\pill_magnesium_crack_282.png", "Assets\\pill_magnesium_crack\\pill_magnesium_crack_283.png", "Assets\\pill_magnesium_crack\\pill_magnesium_crack_284.png", "Assets\\pill_magnesium_crack\\pill_magnesium_crack_285.png", "Assets\\pill_magnesium_crack\\pill_magnesium_crack_286.png", "Assets\\pill_magnesium_crack\\pill_magnesium_crack_287.png", "Assets\\pill_magnesium_crack\\pill_magnesium_crack_288.png", "Assets\\pill_magnesium_crack\\pill_magnesium_crack_289.png"]}, "ROI": {"$id": "51", "$type": "H.VisionMaster.NodeData.ROIPresenters.FromROI, H.VisionMaster.NodeData", "ID": "ce5d9565-732c-4b37-a507-671725ebe619", "Name": "继承"}, "FromROI": {"$ref": "51"}, "DrawROI": {"$id": "52", "$type": "H.VisionMaster.NodeData.ROIPresenters.DrawROI, H.VisionMaster.NodeData", "Rect": "Empty", "ID": "d59f474f-77a6-4a7a-a4d2-acad26b2adf4", "Name": "绘制"}, "InputROI": {"$id": "53", "$type": "H.VisionMaster.NodeData.ROIPresenters.InputROI, H.VisionMaster.NodeData", "ID": "dbdf8e27-e2cc-4619-a3b4-fc06aea9c080", "Name": "输入"}, "PreviewMillisecondsDelay": 0, "InvokeMillisecondsDelay": 0, "FlagLength": 10.0, "DiagramData": {"$ref": "1"}, "Text": "药丸破裂数据源", "FontSize": 12.0, "PortDatas": {"$type": "System.Collections.Generic.List`1[[H.Controls.Diagram.Datas.IPortData, H.Controls.Diagram]], System.Private.CoreLib", "$values": [{"$id": "54", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Top", "NodeID": "d28df650-1c0c-4d70-9e4d-71355014c790", "PortType": "Input", "ID": "684a9aaf-3e9d-4fc4-97f2-32dae03c5b4d"}, {"$id": "55", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Bottom", "NodeID": "d28df650-1c0c-4d70-9e4d-71355014c790", "PortType": "OutPut", "ID": "2c69beb3-1011-4510-bcc4-d16b6d3062f5"}, {"$id": "56", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "NodeID": "d28df650-1c0c-4d70-9e4d-71355014c790", "PortType": "Input", "ID": "50afd178-4397-47c9-a722-9c694508b041"}, {"$id": "57", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Right", "NodeID": "d28df650-1c0c-4d70-9e4d-71355014c790", "PortType": "OutPut", "ID": "63172336-1677-40ee-9276-cc982777cf94"}]}, "IsTemplate": false, "Height": 35.0, "Width": 120.0, "Fill": "#FFFFFFFF", "CornerRadius": 2.0, "Location": "553.3592078189299,653.5851851851851", "ID": "d28df650-1c0c-4d70-9e4d-71355014c790", "Name": "药丸破裂数据源", "Icon": ""}, {"$id": "58", "$type": "H.App.VisionMaster.OpenCV.NodeDatas.SrcImages.OpenCVPipeJointsSrcImageFilesNodeData, H.App.VisionMaster.OpenCV", "UseAutoSwitch": true, "SrcFilePath": "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_01.png", "SrcFilePaths": {"$type": "System.Collections.ObjectModel.ObservableCollection`1[[System.String, System.Private.CoreLib]], System.ObjectModel", "$values": ["Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_01.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_02.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_03.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_04.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_05.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_06.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_07.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_08.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_09.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_10.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_11.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_12.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_13.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_14.png", "Assets\\multi_view_pipe_joints_cam\\multi_view_pipe_joints_cam_0_15.png"]}, "ROI": {"$id": "59", "$type": "H.VisionMaster.NodeData.ROIPresenters.FromROI, H.VisionMaster.NodeData", "ID": "b0473797-72a2-42d2-8e47-f15b56dfea41", "Name": "继承"}, "FromROI": {"$ref": "59"}, "DrawROI": {"$id": "60", "$type": "H.VisionMaster.NodeData.ROIPresenters.DrawROI, H.VisionMaster.NodeData", "Rect": "Empty", "ID": "f5786ae1-7f39-403b-ac00-cc64326e3305", "Name": "绘制"}, "InputROI": {"$id": "61", "$type": "H.VisionMaster.NodeData.ROIPresenters.InputROI, H.VisionMaster.NodeData", "ID": "287dda79-610f-4dc6-a16c-3cd172499086", "Name": "输入"}, "PreviewMillisecondsDelay": 0, "InvokeMillisecondsDelay": 0, "FlagLength": 10.0, "DiagramData": {"$ref": "1"}, "Text": "管道接头图像源", "FontSize": 12.0, "PortDatas": {"$type": "System.Collections.Generic.List`1[[H.Controls.Diagram.Datas.IPortData, H.Controls.Diagram]], System.Private.CoreLib", "$values": [{"$id": "62", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Top", "NodeID": "2248d97c-ab1d-44ec-a81c-fd4ab629f44b", "PortType": "Input", "ID": "72b930ad-da77-4fa9-98a8-70a7cd30d148"}, {"$id": "63", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Bottom", "NodeID": "2248d97c-ab1d-44ec-a81c-fd4ab629f44b", "PortType": "OutPut", "ID": "c193974c-e50f-4c17-9be3-0cefe9aafc50"}, {"$id": "64", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "NodeID": "2248d97c-ab1d-44ec-a81c-fd4ab629f44b", "PortType": "Input", "ID": "226315ac-a9a0-4cb1-aa75-f6513986054d"}, {"$id": "65", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Right", "NodeID": "2248d97c-ab1d-44ec-a81c-fd4ab629f44b", "PortType": "OutPut", "ID": "325e0103-6f03-40a1-b73a-df49755078f2"}]}, "IsTemplate": false, "Height": 35.0, "Width": 120.0, "Fill": "#FFFFFFFF", "CornerRadius": 2.0, "Location": "554.1629629629629,783.5851851851851", "ID": "2248d97c-ab1d-44ec-a81c-fd4ab629f44b", "Name": "管道接头图像源", "Icon": ""}, {"$id": "66", "$type": "H.App.VisionMaster.OpenCV.NodeDatas.SrcImages.OpenCVRadiusGaugesSrcImageFilesNodeData, H.App.VisionMaster.OpenCV", "UseAutoSwitch": true, "SrcFilePath": "Assets\\radius-gauges\\radius-gauges-00.png", "SrcFilePaths": {"$type": "System.Collections.ObjectModel.ObservableCollection`1[[System.String, System.Private.CoreLib]], System.ObjectModel", "$values": ["Assets\\radius-gauges\\radius-gauges-00.png", "Assets\\radius-gauges\\radius-gauges-01.png", "Assets\\radius-gauges\\radius-gauges-02.png", "Assets\\radius-gauges\\radius-gauges-03.png", "Assets\\radius-gauges\\radius-gauges-04.png", "Assets\\radius-gauges\\radius-gauges-05.png", "Assets\\radius-gauges\\radius-gauges-06.png", "Assets\\radius-gauges\\radius-gauges-07.png", "Assets\\radius-gauges\\radius-gauges-08.png", "Assets\\radius-gauges\\radius-gauges-09.png", "Assets\\radius-gauges\\radius-gauges-10.png", "Assets\\radius-gauges\\radius-gauges-11.png", "Assets\\radius-gauges\\radius-gauges-12.png", "Assets\\radius-gauges\\radius-gauges-13.png", "Assets\\radius-gauges\\radius-gauges-14.png", "Assets\\radius-gauges\\radius-gauges-15.png", "Assets\\radius-gauges\\radius-gauges-16.png", "Assets\\radius-gauges\\radius-gauges-17.png", "Assets\\radius-gauges\\radius-gauges-18.png", "Assets\\radius-gauges\\radius-gauges-19.png", "Assets\\radius-gauges\\radius-gauges-20.png"]}, "ROI": {"$id": "67", "$type": "H.VisionMaster.NodeData.ROIPresenters.FromROI, H.VisionMaster.NodeData", "ID": "e5fdc1ff-c36d-478c-8865-896e6aa3ce96", "Name": "继承"}, "FromROI": {"$ref": "67"}, "DrawROI": {"$id": "68", "$type": "H.VisionMaster.NodeData.ROIPresenters.DrawROI, H.VisionMaster.NodeData", "Rect": "Empty", "ID": "a7716aeb-58d3-4da1-bd9b-7071cbfcb9c3", "Name": "绘制"}, "InputROI": {"$id": "69", "$type": "H.VisionMaster.NodeData.ROIPresenters.InputROI, H.VisionMaster.NodeData", "ID": "8fb8df65-43c1-4ccb-b829-d08b9f21ad89", "Name": "输入"}, "PreviewMillisecondsDelay": 0, "InvokeMillisecondsDelay": 0, "FlagLength": 10.0, "DiagramData": {"$ref": "1"}, "Text": "半径量规图像源", "FontSize": 12.0, "PortDatas": {"$type": "System.Collections.Generic.List`1[[H.Controls.Diagram.Datas.IPortData, H.Controls.Diagram]], System.Private.CoreLib", "$values": [{"$id": "70", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Top", "NodeID": "62d7dae7-24ae-44fe-8588-7282741e105a", "PortType": "Input", "ID": "b3246c19-e55e-4549-bab9-e3b9728caf58"}, {"$id": "71", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Bottom", "NodeID": "62d7dae7-24ae-44fe-8588-7282741e105a", "PortType": "OutPut", "ID": "a796b6ed-865c-4cfa-929b-62d6f2d94621"}, {"$id": "72", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "NodeID": "62d7dae7-24ae-44fe-8588-7282741e105a", "PortType": "Input", "ID": "823104a8-03ca-4f73-91c1-6bdc68113a4d"}, {"$id": "73", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Right", "NodeID": "62d7dae7-24ae-44fe-8588-7282741e105a", "PortType": "OutPut", "ID": "87af974a-3c99-4d89-9d9b-71c0c355f796"}]}, "IsTemplate": false, "Height": 35.0, "Width": 120.0, "Fill": "#FFFFFFFF", "CornerRadius": 2.0, "Location": "554.1629629629629,828.5851851851851", "ID": "62d7dae7-24ae-44fe-8588-7282741e105a", "Name": "半径量规图像源", "Icon": ""}, {"$id": "74", "$type": "H.App.VisionMaster.OpenCV.NodeDatas.SrcImages.OpenCVSrcImageFilesNodeData, H.App.VisionMaster.OpenCV", "UseAutoSwitch": true, "SrcFilePath": "Assets\\OpenCV\\00.JPG", "SrcFilePaths": {"$type": "System.Collections.ObjectModel.ObservableCollection`1[[System.String, System.Private.CoreLib]], System.ObjectModel", "$values": ["Assets\\OpenCV\\00.JPG", "Assets\\OpenCV\\01.JPG", "Assets\\OpenCV\\02.JPG", "Assets\\OpenCV\\16bit.png", "Assets\\OpenCV\\aruco_markers_photo.jpg", "Assets\\OpenCV\\aruco_markers_source.jpg", "Assets\\OpenCV\\asahiyama.jpg", "Assets\\OpenCV\\Balloon.png", "Assets\\OpenCV\\binarization_sample.bmp", "Assets\\OpenCV\\box.png", "Assets\\OpenCV\\box_in_scene.png", "Assets\\OpenCV\\cake.bmp", "Assets\\OpenCV\\cat.jpg", "Assets\\OpenCV\\circle.png", "Assets\\OpenCV\\cvmorph.png", "Assets\\OpenCV\\fruits.jpg", "Assets\\OpenCV\\Girl.bmp", "Assets\\OpenCV\\goryokaku.jpg", "Assets\\OpenCV\\hand_p.jpg", "Assets\\OpenCV\\left01.jpg", "Assets\\OpenCV\\left02.jpg", "Assets\\OpenCV\\left03.jpg", "Assets\\OpenCV\\left04.jpg", "Assets\\OpenCV\\left05.jpg", "Assets\\OpenCV\\left06.jpg", "Assets\\OpenCV\\left07.jpg", "Assets\\OpenCV\\left08.jpg", "Assets\\OpenCV\\left09.jpg", "Assets\\OpenCV\\left10.jpg", "Assets\\OpenCV\\left11.jpg", "Assets\\OpenCV\\left12.jpg", "Assets\\OpenCV\\left13.jpg", "Assets\\OpenCV\\lenna.png", "Assets\\OpenCV\\lenna511.png", "Assets\\OpenCV\\maltese.jpg", "Assets\\OpenCV\\Mandrill.bmp", "Assets\\OpenCV\\match1.png", "Assets\\OpenCV\\match2.png", "Assets\\OpenCV\\penguin1.png", "Assets\\OpenCV\\penguin1b.png", "Assets\\OpenCV\\penguin2.png", "Assets\\OpenCV\\pentagon.png", "Assets\\OpenCV\\pic1.png", "Assets\\OpenCV\\pic2.png", "Assets\\OpenCV\\pic3.png", "Assets\\OpenCV\\pic4.png", "Assets\\OpenCV\\pic5.png", "Assets\\OpenCV\\pic6.png", "Assets\\OpenCV\\right01.jpg", "Assets\\OpenCV\\right02.jpg", "Assets\\OpenCV\\right03.jpg", "Assets\\OpenCV\\right04.jpg", "Assets\\OpenCV\\right05.jpg", "Assets\\OpenCV\\right06.jpg", "Assets\\OpenCV\\right07.jpg", "Assets\\OpenCV\\right08.jpg", "Assets\\OpenCV\\right09.jpg", "Assets\\OpenCV\\right10.jpg", "Assets\\OpenCV\\right11.jpg", "Assets\\OpenCV\\right12.jpg", "Assets\\OpenCV\\right13.jpg", "Assets\\OpenCV\\shapes.png", "Assets\\OpenCV\\space_shuttle.jpg", "Assets\\OpenCV\\tsukuba_left.png", "Assets\\OpenCV\\tsukuba_right.png", "Assets\\OpenCV\\very_old_newspaper.png", "Assets\\OpenCV\\walkman.jpg", "Assets\\OpenCV\\yalta.jpg"]}, "ROI": {"$id": "75", "$type": "H.VisionMaster.NodeData.ROIPresenters.FromROI, H.VisionMaster.NodeData", "ID": "e82f48de-218f-4082-9ee4-e576fce621a1", "Name": "继承"}, "FromROI": {"$ref": "75"}, "DrawROI": {"$id": "76", "$type": "H.VisionMaster.NodeData.ROIPresenters.DrawROI, H.VisionMaster.NodeData", "Rect": "Empty", "ID": "38c79b35-7763-46e8-a8dd-165d1e2d6cbe", "Name": "绘制"}, "InputROI": {"$id": "77", "$type": "H.VisionMaster.NodeData.ROIPresenters.InputROI, H.VisionMaster.NodeData", "ID": "045f0205-310a-4734-bfb7-b1bc292cddae", "Name": "输入"}, "PreviewMillisecondsDelay": 0, "InvokeMillisecondsDelay": 0, "FlagLength": 10.0, "DiagramData": {"$ref": "1"}, "Text": "OpenCV图像源", "FontSize": 12.0, "PortDatas": {"$type": "System.Collections.Generic.List`1[[H.Controls.Diagram.Datas.IPortData, H.Controls.Diagram]], System.Private.CoreLib", "$values": [{"$id": "78", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Top", "NodeID": "3ac18e5a-6fa9-4338-afef-cd925339219e", "PortType": "Input", "ID": "c173d642-aeba-4dbc-aa69-ed88f646a796"}, {"$id": "79", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Bottom", "NodeID": "3ac18e5a-6fa9-4338-afef-cd925339219e", "PortType": "OutPut", "ID": "edd157d1-8787-457e-a0ab-058062aacf8d"}, {"$id": "80", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "NodeID": "3ac18e5a-6fa9-4338-afef-cd925339219e", "PortType": "Input", "ID": "7530461c-592b-4608-8647-880c62eecde2"}, {"$id": "81", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Right", "NodeID": "3ac18e5a-6fa9-4338-afef-cd925339219e", "PortType": "OutPut", "ID": "251c2655-5fa4-4d99-b49a-ab366a4c22e4"}]}, "IsTemplate": false, "Height": 35.0, "Width": 120.0, "Fill": "#FFFFFFFF", "CornerRadius": 2.0, "Location": "554.1629629629629,873.5851851851851", "ID": "3ac18e5a-6fa9-4338-afef-cd925339219e", "Name": "OpenCV图像源", "Icon": ""}, {"$id": "82", "$type": "H.App.VisionMaster.OpenCV.NodeDatas.SrcImages.PersonSrcImageFilesNodeData, H.App.VisionMaster.OpenCV", "UseAutoSwitch": true, "SrcFilePath": "Assets\\Person\\009445.jpg", "SrcFilePaths": {"$type": "System.Collections.ObjectModel.ObservableCollection`1[[System.String, System.Private.CoreLib]], System.ObjectModel", "$values": ["Assets\\Person\\009445.jpg", "Assets\\Person\\009453.jpg", "Assets\\Person\\009456.jpg", "Assets\\Person\\1.jpg", "Assets\\Person\\Image1.jpg", "Assets\\Person\\Image4.jpg", "Assets\\Person\\Image5.jpg", "Assets\\Person\\woman.jpg", "Assets\\Person\\yolov6-inference-cycling.jpg", "Assets\\Person\\yolov6-inference-soccer.jpg"]}, "ROI": {"$id": "83", "$type": "H.VisionMaster.NodeData.ROIPresenters.FromROI, H.VisionMaster.NodeData", "ID": "f20ea507-d570-4639-bb83-8c43ab3c8c18", "Name": "继承"}, "FromROI": {"$ref": "83"}, "DrawROI": {"$id": "84", "$type": "H.VisionMaster.NodeData.ROIPresenters.DrawROI, H.VisionMaster.NodeData", "Rect": "Empty", "ID": "eb88d8ae-e789-4280-93ee-e963dcf24a8d", "Name": "绘制"}, "InputROI": {"$id": "85", "$type": "H.VisionMaster.NodeData.ROIPresenters.InputROI, H.VisionMaster.NodeData", "ID": "d2e3369c-6b7e-42c4-89ec-24998cc24f18", "Name": "输入"}, "PreviewMillisecondsDelay": 0, "InvokeMillisecondsDelay": 0, "FlagLength": 10.0, "DiagramData": {"$ref": "1"}, "Text": "人物图像源", "FontSize": 12.0, "PortDatas": {"$type": "System.Collections.Generic.List`1[[H.Controls.Diagram.Datas.IPortData, H.Controls.Diagram]], System.Private.CoreLib", "$values": [{"$id": "86", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Top", "NodeID": "7bf16ac2-be09-40e6-b76c-e66f0f1e35a6", "PortType": "Input", "ID": "9c0f9344-4942-4a86-8817-462445412e18"}, {"$id": "87", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Bottom", "NodeID": "7bf16ac2-be09-40e6-b76c-e66f0f1e35a6", "PortType": "OutPut", "ID": "ebf69a8c-cc5d-41c8-b31c-b88d80e82c5f"}, {"$id": "88", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "NodeID": "7bf16ac2-be09-40e6-b76c-e66f0f1e35a6", "PortType": "Input", "ID": "c96ebb22-9a6e-4b0d-9401-7d8f8e1a42a0"}, {"$id": "89", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Right", "NodeID": "7bf16ac2-be09-40e6-b76c-e66f0f1e35a6", "PortType": "OutPut", "ID": "e2e38f5b-4370-4c32-ad17-17c4fee39f57"}]}, "IsTemplate": false, "Height": 35.0, "Width": 120.0, "Fill": "#FFFFFFFF", "CornerRadius": 2.0, "Location": "554.1629629629629,923.5851851851851", "ID": "7bf16ac2-be09-40e6-b76c-e66f0f1e35a6", "Name": "人物图像源", "Icon": ""}]}, "LinkDatas": {"$type": "System.Collections.Generic.List`1[[H.Controls.Diagram.Datas.ILinkData, H.Controls.Diagram]], System.Private.CoreLib", "$values": [{"$id": "90", "$type": "H.Controls.Diagram.Presenter.LinkDatas.FlowableLinkData, H.Controls.Diagram.Presenter", "FontSize": 12.0, "Stroke": "#FFFFA500", "FromNodeID": "a47b58b8-1bfb-466b-b1a4-1ff892a6a162", "ToNodeID": "b3500e99-cd63-452c-9252-581c907d18af", "FromPortID": "2961ad0e-4671-43d7-be2a-536dd258ae8f", "ToPortID": "6cc92402-bfc9-4fca-b5b7-35a1fe284d40", "ID": "97de284b-3516-473d-98ad-9eb47bb611bd", "Name": "连线"}, {"$id": "91", "$type": "H.Controls.Diagram.Presenter.LinkDatas.FlowableLinkData, H.Controls.Diagram.Presenter", "FontSize": 12.0, "Stroke": "#FFFFA500", "FromNodeID": "a47b58b8-1bfb-466b-b1a4-1ff892a6a162", "ToNodeID": "0cba1e1e-2fe4-40ff-8ce5-d3b20b72c904", "FromPortID": "2961ad0e-4671-43d7-be2a-536dd258ae8f", "ToPortID": "f3c328a7-f326-476e-9cbd-e712b2201682", "ID": "e74eb7b4-bcac-4abe-978e-4f257978a33a", "Name": "连线"}, {"$id": "92", "$type": "H.Controls.Diagram.Presenter.LinkDatas.FlowableLinkData, H.Controls.Diagram.Presenter", "FontSize": 12.0, "Stroke": "#FFFFA500", "FromNodeID": "a47b58b8-1bfb-466b-b1a4-1ff892a6a162", "ToNodeID": "b226a5e1-aacc-41fc-8926-37133d3374f6", "FromPortID": "2961ad0e-4671-43d7-be2a-536dd258ae8f", "ToPortID": "0b8cdf23-2fda-4b90-8ae8-18b6c77e122a", "ID": "bdee6292-4e70-4a6e-a751-62ac6d4f2b52", "Name": "连线"}, {"$id": "93", "$type": "H.Controls.Diagram.Presenter.LinkDatas.FlowableLinkData, H.Controls.Diagram.Presenter", "FontSize": 12.0, "Stroke": "#FFFFA500", "FromNodeID": "a47b58b8-1bfb-466b-b1a4-1ff892a6a162", "ToNodeID": "6cc4957d-5bb7-4949-9f79-bd3d0d6a44c5", "FromPortID": "2961ad0e-4671-43d7-be2a-536dd258ae8f", "ToPortID": "7d407fe7-58fe-4c12-8066-af444db4fc91", "ID": "ed038a53-1a18-4d97-8a1e-b8e51f71f81d", "Name": "连线"}, {"$id": "94", "$type": "H.Controls.Diagram.Presenter.LinkDatas.FlowableLinkData, H.Controls.Diagram.Presenter", "FontSize": 12.0, "Stroke": "#FFFFA500", "FromNodeID": "a47b58b8-1bfb-466b-b1a4-1ff892a6a162", "ToNodeID": "efd08586-252a-4a3c-8b50-1438035310f4", "FromPortID": "2961ad0e-4671-43d7-be2a-536dd258ae8f", "ToPortID": "bfe7f009-51ce-4f7d-ab57-e3bb029512b5", "ID": "161e618f-3d70-4dda-8de5-5ddaeb6ed3b1", "Name": "连线"}, {"$id": "95", "$type": "H.Controls.Diagram.Presenter.LinkDatas.FlowableLinkData, H.Controls.Diagram.Presenter", "FontSize": 12.0, "Stroke": "#FFFFA500", "FromNodeID": "a47b58b8-1bfb-466b-b1a4-1ff892a6a162", "ToNodeID": "d28df650-1c0c-4d70-9e4d-71355014c790", "FromPortID": "2961ad0e-4671-43d7-be2a-536dd258ae8f", "ToPortID": "50afd178-4397-47c9-a722-9c694508b041", "ID": "a1a8bc4e-26d6-4cf8-a81a-57c0924dfedb", "Name": "连线"}, {"$id": "96", "$type": "H.Controls.Diagram.Presenter.LinkDatas.FlowableLinkData, H.Controls.Diagram.Presenter", "FontSize": 12.0, "Stroke": "#FFFFA500", "FromNodeID": "a47b58b8-1bfb-466b-b1a4-1ff892a6a162", "ToNodeID": "2248d97c-ab1d-44ec-a81c-fd4ab629f44b", "FromPortID": "2961ad0e-4671-43d7-be2a-536dd258ae8f", "ToPortID": "226315ac-a9a0-4cb1-aa75-f6513986054d", "ID": "364eb311-7f5b-440e-951e-0651c5ff44ce", "Name": "连线"}, {"$id": "97", "$type": "H.Controls.Diagram.Presenter.LinkDatas.FlowableLinkData, H.Controls.Diagram.Presenter", "FontSize": 12.0, "Stroke": "#FFFFA500", "FromNodeID": "a47b58b8-1bfb-466b-b1a4-1ff892a6a162", "ToNodeID": "62d7dae7-24ae-44fe-8588-7282741e105a", "FromPortID": "2961ad0e-4671-43d7-be2a-536dd258ae8f", "ToPortID": "823104a8-03ca-4f73-91c1-6bdc68113a4d", "ID": "d6400392-e769-45a5-a0cd-36caf68f3f0e", "Name": "连线"}, {"$id": "98", "$type": "H.Controls.Diagram.Presenter.LinkDatas.FlowableLinkData, H.Controls.Diagram.Presenter", "FontSize": 12.0, "Stroke": "#FFFFA500", "FromNodeID": "a47b58b8-1bfb-466b-b1a4-1ff892a6a162", "ToNodeID": "3ac18e5a-6fa9-4338-afef-cd925339219e", "FromPortID": "2961ad0e-4671-43d7-be2a-536dd258ae8f", "ToPortID": "7530461c-592b-4608-8647-880c62eecde2", "ID": "13aca557-8a57-432c-a59f-e6909ae5a6ae", "Name": "连线"}, {"$id": "99", "$type": "H.Controls.Diagram.Presenter.LinkDatas.FlowableLinkData, H.Controls.Diagram.Presenter", "FontSize": 12.0, "Stroke": "#FFFFA500", "FromNodeID": "a47b58b8-1bfb-466b-b1a4-1ff892a6a162", "ToNodeID": "7bf16ac2-be09-40e6-b76c-e66f0f1e35a6", "FromPortID": "2961ad0e-4671-43d7-be2a-536dd258ae8f", "ToPortID": "c96ebb22-9a6e-4b0d-9401-7d8f8e1a42a0", "ID": "091810db-8af8-4b2b-9ee5-abc6e088d516", "Name": "连线"}]}}, "ID": "e369cecd-48b8-495f-840a-2311e21fa8ed"}, {"$id": "100", "$type": "H.App.VisionMaster.OpenCV.DiagramDatas.OpenCVVisionDiagramData, H.App.VisionMaster.OpenCV", "UseFlowableSelectToRunning": true, "Name": "视频数据源", "Width": 1000.0, "Height": 1500.0, "Location": "1000,650", "Message": "选中节点 - 本地视频源", "Datas": {"$type": "H.Controls.Diagram.Presenter.DiagramDatas.Base.Datas, H.Controls.Diagram.Presenter", "NodeDatas": {"$type": "System.Collections.Generic.List`1[[H.Controls.Diagram.Datas.INodeData, H.Controls.Diagram]], System.Private.CoreLib", "$values": [{"$id": "101", "$type": "H.VisionMaster.OpenCV.NodeDatas.Src.SrcVideoFilesNodeData, H.VisionMaster.OpenCV", "UseAutoSwitch": true, "SrcFilePath": "Assets\\Videos\\01.mp4", "SrcFilePaths": {"$type": "System.Collections.ObjectModel.ObservableCollection`1[[System.String, System.Private.CoreLib]], System.ObjectModel", "$values": ["Assets\\Videos\\01.mp4", "Assets\\Videos\\baby.mp4", "Assets\\Videos\\balltest.mp4", "Assets\\Videos\\bike.avi", "Assets\\Videos\\box.mp4", "Assets\\Videos\\cars.mp4", "Assets\\Videos\\color_object.mp4", "Assets\\Videos\\crowd.mp4", "Assets\\Videos\\MOT17-04-DPM.mp4", "Assets\\Videos\\mulballs.mp4", "Assets\\Videos\\qRcode-sample.mp4", "Assets\\Videos\\road_line.mp4"]}, "ROI": {"$id": "102", "$type": "H.VisionMaster.NodeData.ROIPresenters.FromROI, H.VisionMaster.NodeData", "ID": "abfb7873-6116-4266-9062-25a13bf94cfc", "Name": "继承"}, "FromROI": {"$ref": "102"}, "DrawROI": {"$id": "103", "$type": "H.VisionMaster.NodeData.ROIPresenters.DrawROI, H.VisionMaster.NodeData", "Rect": "Empty", "ID": "9a3b8388-1f8e-4158-9eab-38835041ad36", "Name": "绘制"}, "InputROI": {"$id": "104", "$type": "H.VisionMaster.NodeData.ROIPresenters.InputROI, H.VisionMaster.NodeData", "ID": "7591a149-3936-46af-b54a-e275b2b07306", "Name": "输入"}, "PreviewMillisecondsDelay": 0, "InvokeMillisecondsDelay": 0, "FlagLength": 10.0, "DiagramData": {"$ref": "100"}, "Text": "本地视频源", "FontSize": 12.0, "PortDatas": {"$type": "System.Collections.Generic.List`1[[H.Controls.Diagram.Datas.IPortData, H.Controls.Diagram]], System.Private.CoreLib", "$values": [{"$id": "105", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Top", "NodeID": "f70160f3-2a75-4c01-a39c-f556fdd0a87c", "PortType": "Input", "ID": "3a1b4b45-abc8-45a1-b155-76b48d89ef20"}, {"$id": "106", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Bottom", "NodeID": "f70160f3-2a75-4c01-a39c-f556fdd0a87c", "PortType": "OutPut", "ID": "3cfed85a-8056-4b68-83d0-230b6488a87a"}, {"$id": "107", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "NodeID": "f70160f3-2a75-4c01-a39c-f556fdd0a87c", "PortType": "Input", "ID": "2203ea45-502a-4ace-bc04-828abb2ad6a7"}, {"$id": "108", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Right", "NodeID": "f70160f3-2a75-4c01-a39c-f556fdd0a87c", "PortType": "OutPut", "ID": "f8b629c7-2920-4b00-bfd9-8452ca47336d"}]}, "IsTemplate": false, "Height": 35.0, "Width": 120.0, "Fill": "#FFFFFFFF", "IsSelected": true, "CornerRadius": 2.0, "Location": "495.8666666666666,651.57037037037", "ID": "f70160f3-2a75-4c01-a39c-f556fdd0a87c", "Name": "本地视频源", "Icon": ""}]}, "LinkDatas": {"$type": "System.Collections.Generic.List`1[[H.Controls.Diagram.Datas.ILinkData, H.Controls.Diagram]], System.Private.CoreLib", "$values": []}}, "ID": "362198de-4223-4838-8a69-ec973d2dc765"}, {"$id": "109", "$type": "H.App.VisionMaster.OpenCV.DiagramDatas.OpenCVVisionDiagramData, H.App.VisionMaster.OpenCV", "UseFlowableSelectToRunning": true, "Name": "摄像头", "Width": 1000.0, "Height": 1500.0, "Location": "1000,650", "Message": "添加节点 - 摄像头", "Datas": {"$type": "H.Controls.Diagram.Presenter.DiagramDatas.Base.Datas, H.Controls.Diagram.Presenter", "NodeDatas": {"$type": "System.Collections.Generic.List`1[[H.Controls.Diagram.Datas.INodeData, H.Controls.Diagram]], System.Private.CoreLib", "$values": [{"$id": "110", "$type": "H.VisionMaster.OpenCV.NodeDatas.Src.CameraCaptureNodeData, H.VisionMaster.OpenCV", "UseAutoSwitch": true, "SrcFilePaths": {"$type": "System.Collections.ObjectModel.ObservableCollection`1[[System.String, System.Private.CoreLib]], System.ObjectModel", "$values": []}, "ROI": {"$id": "111", "$type": "H.VisionMaster.NodeData.ROIPresenters.FromROI, H.VisionMaster.NodeData", "ID": "18c746e7-3d05-4df1-9059-66b2fa58d926", "Name": "继承"}, "FromROI": {"$ref": "111"}, "DrawROI": {"$id": "112", "$type": "H.VisionMaster.NodeData.ROIPresenters.DrawROI, H.VisionMaster.NodeData", "Rect": "Empty", "ID": "ced4f8ce-319e-403c-bf0c-59bccd9746db", "Name": "绘制"}, "InputROI": {"$id": "113", "$type": "H.VisionMaster.NodeData.ROIPresenters.InputROI, H.VisionMaster.NodeData", "ID": "b7140b4d-8524-4078-98ff-0962ef7fea61", "Name": "输入"}, "PreviewMillisecondsDelay": 0, "InvokeMillisecondsDelay": 0, "FlagLength": 10.0, "DiagramData": {"$ref": "109"}, "Text": "摄像头", "FontSize": 12.0, "PortDatas": {"$type": "System.Collections.Generic.List`1[[H.Controls.Diagram.Datas.IPortData, H.Controls.Diagram]], System.Private.CoreLib", "$values": [{"$id": "114", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Top", "NodeID": "fa849c36-4b26-464a-bf01-ee762adbdbeb", "PortType": "Input", "ID": "c4574bde-6e1a-4c21-b919-3c6296f1bc88"}, {"$id": "115", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Bottom", "NodeID": "fa849c36-4b26-464a-bf01-ee762adbdbeb", "PortType": "OutPut", "ID": "b529289d-cc7d-4db1-a71d-80321e6db7ea"}, {"$id": "116", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "NodeID": "fa849c36-4b26-464a-bf01-ee762adbdbeb", "PortType": "Input", "ID": "c82fddf8-7e70-41b1-a8d5-a683618bf242"}, {"$id": "117", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Right", "NodeID": "fa849c36-4b26-464a-bf01-ee762adbdbeb", "PortType": "OutPut", "ID": "5c2b9041-4570-4d8e-bc23-cfe946b36f2f"}]}, "IsTemplate": false, "Height": 35.0, "Width": 120.0, "Fill": "#FFFFFFFF", "CornerRadius": 2.0, "Location": "487.9555555555555,666.2296296296296", "ID": "fa849c36-4b26-464a-bf01-ee762adbdbeb", "Name": "摄像头", "Icon": ""}]}, "LinkDatas": {"$type": "System.Collections.Generic.List`1[[H.Controls.Diagram.Datas.ILinkData, H.Controls.Diagram]], System.Private.CoreLib", "$values": []}}, "ID": "025a76e5-f803-4310-bdc7-6968528c0209"}]}