{"$type": "System.Collections.ObjectModel.ObservableCollection`1[[H.VisionMaster.DiagramData.IVisionDiagramData, H.VisionMaster.DiagramData]], System.ObjectModel", "$values": [{"$id": "1", "$type": "H.App.VisionMaster.OpenCV.DiagramDatas.OpenCVVisionDiagramData, H.App.VisionMaster.OpenCV", "RunModeResult": "True", "UseFlowableSelectToRunning": true, "Name": "机器视觉流程", "Width": 1000.0, "Height": 1500.0, "Location": "1000,650", "Message": "运行成功", "Datas": {"$type": "H.Controls.Diagram.Presenter.DiagramDatas.Base.Datas, H.Controls.Diagram.Presenter", "NodeDatas": {"$type": "System.Collections.Generic.List`1[[H.Controls.Diagram.Datas.INodeData, H.Controls.Diagram]], System.Private.CoreLib", "$values": [{"$id": "2", "$type": "H.App.VisionMaster.OpenCV.NodeDatas.AgeInferOnnxNodeData, H.App.VisionMaster.OpenCV", "AgeResult": 29.64099884033203, "InputSize": "224,224", "BlobMean": {"$id": "3", "$type": "OpenCvSharp.Scalar, OpenCvSharp", "Val0": 0.485, "Val1": 0.456, "Val2": 0.406}, "BlobStd": {"$id": "4", "$type": "OpenCvSharp.Scalar, OpenCvSharp", "Val0": 0.229, "Val1": 0.224, "Val2": 0.225}, "ModelPath": "Assets\\Onnx\\age_efficientnet_b2.onnx", "OutputRowIndex": 0, "OutputColumnIndex": 1, "ROI": {"$id": "5", "$type": "H.VisionMaster.NodeData.ROIPresenters.FromROI, H.VisionMaster.NodeData", "ID": "e5014460-029d-49ee-b1dd-3e9ae913ecfa", "Name": "继承"}, "FromROI": {"$ref": "5"}, "DrawROI": {"$id": "6", "$type": "H.VisionMaster.NodeData.ROIPresenters.DrawROI, H.VisionMaster.NodeData", "Rect": "Empty", "ID": "1c94702e-fde2-4b76-8a63-1e869b6a4f2b", "Name": "绘制"}, "InputROI": {"$id": "7", "$type": "H.VisionMaster.NodeData.ROIPresenters.InputROI, H.VisionMaster.NodeData", "ID": "b651a2d5-6d59-4340-83dd-7d5b82f5ba10", "Name": "输入"}, "PreviewMillisecondsDelay": 0, "InvokeMillisecondsDelay": 0, "FlagLength": 10.0, "State": "Success", "TimeSpan": "00:00:00.6258169", "Message": "推测结果:30岁", "DiagramData": {"$ref": "1"}, "Text": "年龄推测", "FontSize": 12.0, "PortDatas": {"$type": "System.Collections.Generic.List`1[[H.Controls.Diagram.Datas.IPortData, H.Controls.Diagram]], System.Private.CoreLib", "$values": [{"$id": "8", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "State": "Wait", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Top", "NodeID": "f694edb2-128b-40f9-a7d8-06ee95ca4d0b", "PortType": "Input", "ID": "494e53b1-2185-47b7-bf32-4d4a1abdc3c0"}, {"$id": "9", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "State": "Wait", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Bottom", "NodeID": "f694edb2-128b-40f9-a7d8-06ee95ca4d0b", "PortType": "OutPut", "ID": "1cd55525-794e-472c-9ea5-867bff110213"}, {"$id": "10", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "State": "Wait", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "NodeID": "f694edb2-128b-40f9-a7d8-06ee95ca4d0b", "PortType": "Input", "ID": "e8b0cfab-177c-4aee-a190-65857cc2a214"}, {"$id": "11", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "State": "Wait", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Right", "NodeID": "f694edb2-128b-40f9-a7d8-06ee95ca4d0b", "PortType": "OutPut", "ID": "2d168a43-2055-427e-bbbd-01de35573e51"}]}, "IsTemplate": false, "Height": 35.0, "Width": 120.0, "Fill": "#FFFFFFFF", "CornerRadius": 2.0, "Location": "521.0849315068493,681.4657534246572", "ID": "f694edb2-128b-40f9-a7d8-06ee95ca4d0b", "Name": "年龄推测", "Icon": ""}, {"$id": "12", "$type": "H.App.VisionMaster.OpenCV.NodeDatas.SrcImages.PersonSrcImageFilesNodeData, H.App.VisionMaster.OpenCV", "PixelWidth": 178, "PixelHeight": 218, "ImageColorType": 16, "UseAutoSwitch": true, "SrcFilePath": "Assets\\Person\\009445.jpg", "SrcFilePaths": {"$type": "System.Collections.ObjectModel.ObservableCollection`1[[System.String, System.Private.CoreLib]], System.ObjectModel", "$values": ["Assets\\Person\\009445.jpg", "Assets\\Person\\009453.jpg", "Assets\\Person\\009456.jpg", "Assets\\Person\\1.jpg", "Assets\\Person\\Image1.jpg", "Assets\\Person\\Image4.jpg", "Assets\\Person\\Image5.jpg", "Assets\\Person\\woman.jpg", "Assets\\Person\\yolov6-inference-cycling.jpg", "Assets\\Person\\yolov6-inference-soccer.jpg", "E:\\图片\\OpenCV\\0.jpg", "E:\\图片\\OpenCV\\0.png", "E:\\图片\\OpenCV\\00.JPG", "E:\\图片\\OpenCV\\000001.jpg", "E:\\图片\\OpenCV\\000131.jpg", "E:\\图片\\OpenCV\\000132.jpg", "E:\\图片\\OpenCV\\000133.jpg", "E:\\图片\\OpenCV\\000135.jpg", "E:\\图片\\OpenCV\\000136.jpg", "E:\\图片\\OpenCV\\000139.jpg", "E:\\图片\\OpenCV\\000140.jpg", "E:\\图片\\OpenCV\\000141.jpg", "E:\\图片\\OpenCV\\000142.jpg", "E:\\图片\\OpenCV\\000143.jpg", "E:\\图片\\OpenCV\\000145.jpg", "E:\\图片\\OpenCV\\000147.jpg", "E:\\图片\\OpenCV\\000150.jpg", "E:\\图片\\OpenCV\\000151.jpg", "E:\\图片\\OpenCV\\000152.jpg", "E:\\图片\\OpenCV\\000153.jpg", "E:\\图片\\OpenCV\\000154.jpg", "E:\\图片\\OpenCV\\000155.jpg", "E:\\图片\\OpenCV\\000156.jpg", "E:\\图片\\OpenCV\\000157.jpg", "E:\\图片\\OpenCV\\000158.jpg", "E:\\图片\\OpenCV\\000160.jpg", "E:\\图片\\OpenCV\\000161.jpg", "E:\\图片\\OpenCV\\000162.jpg", "E:\\图片\\OpenCV\\000163.jpg", "E:\\图片\\OpenCV\\000164.jpg", "E:\\图片\\OpenCV\\000165.jpg", "E:\\图片\\OpenCV\\000166.jpg", "E:\\图片\\OpenCV\\000170.jpg", "E:\\图片\\OpenCV\\000171.jpg", "E:\\图片\\OpenCV\\000172.jpg", "E:\\图片\\OpenCV\\000173.jpg", "E:\\图片\\OpenCV\\000174.jpg", "E:\\图片\\OpenCV\\000175.jpg", "E:\\图片\\OpenCV\\000176.jpg", "E:\\图片\\OpenCV\\000178.jpg", "E:\\图片\\OpenCV\\000179.jpg", "E:\\图片\\OpenCV\\000180.jpg", "E:\\图片\\OpenCV\\000182.jpg", "E:\\图片\\OpenCV\\000183.jpg", "E:\\图片\\OpenCV\\000185.jpg", "E:\\图片\\OpenCV\\000186.jpg", "E:\\图片\\OpenCV\\000187.jpg", "E:\\图片\\OpenCV\\000188.jpg", "E:\\图片\\OpenCV\\000190.jpg", "E:\\图片\\OpenCV\\000191.jpg", "E:\\图片\\OpenCV\\000192.jpg", "E:\\图片\\OpenCV\\000193.jpg", "E:\\图片\\OpenCV\\000194.jpg", "E:\\图片\\OpenCV\\000196.jpg", "E:\\图片\\OpenCV\\000197.jpg", "E:\\图片\\OpenCV\\000198.jpg", "E:\\图片\\OpenCV\\000200.jpg", "E:\\图片\\OpenCV\\00056221.jpg", "E:\\图片\\OpenCV\\00057937.jpg", "E:\\图片\\OpenCV\\00059985.jpg", "E:\\图片\\OpenCV\\00077949.jpg", "E:\\图片\\OpenCV\\00111002.jpg", "E:\\图片\\OpenCV\\00207393.jpg", "E:\\图片\\OpenCV\\009391.jpg", "E:\\图片\\OpenCV\\009392.jpg", "E:\\图片\\OpenCV\\009393.jpg", "E:\\图片\\OpenCV\\009394.jpg", "E:\\图片\\OpenCV\\009395.jpg", "E:\\图片\\OpenCV\\009396.jpg", "E:\\图片\\OpenCV\\009399.jpg", "E:\\图片\\OpenCV\\009400.jpg", "E:\\图片\\OpenCV\\009401.jpg", "E:\\图片\\OpenCV\\009402.jpg", "E:\\图片\\OpenCV\\009403.jpg", "E:\\图片\\OpenCV\\009405.jpg", "E:\\图片\\OpenCV\\009407.jpg", "E:\\图片\\OpenCV\\009408.jpg", "E:\\图片\\OpenCV\\009409.jpg", "E:\\图片\\OpenCV\\009410.jpg", "E:\\图片\\OpenCV\\009412.jpg", "E:\\图片\\OpenCV\\009414.jpg", "E:\\图片\\OpenCV\\009415.jpg", "E:\\图片\\OpenCV\\009416.jpg", "E:\\图片\\OpenCV\\009417.jpg", "E:\\图片\\OpenCV\\009418.jpg", "E:\\图片\\OpenCV\\009419.jpg", "E:\\图片\\OpenCV\\009420.jpg", "E:\\图片\\OpenCV\\009421.jpg", "E:\\图片\\OpenCV\\009422.jpg", "E:\\图片\\OpenCV\\009423.jpg", "E:\\图片\\OpenCV\\009425.jpg", "E:\\图片\\OpenCV\\009426.jpg", "E:\\图片\\OpenCV\\009427.jpg", "E:\\图片\\OpenCV\\009428.jpg", "E:\\图片\\OpenCV\\009429.jpg", "E:\\图片\\OpenCV\\009430.jpg", "E:\\图片\\OpenCV\\009431.jpg", "E:\\图片\\OpenCV\\009432.jpg", "E:\\图片\\OpenCV\\009433.jpg", "E:\\图片\\OpenCV\\009434.jpg", "E:\\图片\\OpenCV\\009435.jpg", "E:\\图片\\OpenCV\\009436.jpg", "E:\\图片\\OpenCV\\009437.jpg", "E:\\图片\\OpenCV\\009438.jpg", "E:\\图片\\OpenCV\\009439.jpg", "E:\\图片\\OpenCV\\009440.jpg", "E:\\图片\\OpenCV\\009441.jpg", "E:\\图片\\OpenCV\\009442.jpg", "E:\\图片\\OpenCV\\009443.jpg", "E:\\图片\\OpenCV\\009444.jpg", "E:\\图片\\OpenCV\\009445.jpg", "E:\\图片\\OpenCV\\009447.jpg", "E:\\图片\\OpenCV\\009448.jpg", "E:\\图片\\OpenCV\\009450.jpg", "E:\\图片\\OpenCV\\009451.jpg", "E:\\图片\\OpenCV\\009452.jpg", "E:\\图片\\OpenCV\\009453.jpg", "E:\\图片\\OpenCV\\009454.jpg", "E:\\图片\\OpenCV\\009455.jpg", "E:\\图片\\OpenCV\\009456.jpg", "E:\\图片\\OpenCV\\009457.jpg", "E:\\图片\\OpenCV\\009458.jpg", "E:\\图片\\OpenCV\\009459.jpg", "E:\\图片\\OpenCV\\009460.jpg", "E:\\图片\\OpenCV\\009461.jpg", "E:\\图片\\OpenCV\\009995.jpg", "E:\\图片\\OpenCV\\009996.jpg", "E:\\图片\\OpenCV\\009997.jpg", "E:\\图片\\OpenCV\\009998.jpg", "E:\\图片\\OpenCV\\009999.jpg", "E:\\图片\\OpenCV\\01.JPG", "E:\\图片\\OpenCV\\012166efd5b0c20dbf846c713af257a78251e2f3.jpg", "E:\\图片\\OpenCV\\02.JPG", "E:\\图片\\OpenCV\\1.jpg", "E:\\图片\\OpenCV\\1.png", "E:\\图片\\OpenCV\\128C.png", "E:\\图片\\OpenCV\\134e302e4b799d631591ab22db6ec0b47b8134f1.jpg", "E:\\图片\\OpenCV\\148c405072f9d5501bcb89c99f2667ceb75d6258.jpg", "E:\\图片\\OpenCV\\149521354b3e974148ef68fc745060f78313e3c7.jpg", "E:\\图片\\OpenCV\\153aacbc959e57d1b4e8f8642ad080e7ab23a681.jpg", "E:\\图片\\OpenCV\\16562069787_a53a8cdccf_n.jpg", "E:\\图片\\OpenCV\\167afe6d1f7406de29b43f8bc2e71090c1e357e6.jpg", "E:\\图片\\OpenCV\\17-license-plate.jpg", "E:\\图片\\OpenCV\\177ac23c1e427c46e059c156fe8421e46afc7e0f.jpg", "E:\\图片\\OpenCV\\177f46fa63c4951fe783ad2880f21aa84119ef83.jpg", "E:\\图片\\OpenCV\\181d4e43d7586f7b20465c796372c56f3a818627.jpg", "E:\\图片\\OpenCV\\185c0e326b2eb32bac82b157a8fe4eb7cbc0cdee.jpg", "E:\\图片\\OpenCV\\186b2bc2fe09d2991566f400ec8404d096c03a0e.jpg", "E:\\图片\\OpenCV\\188a91e594abfc117a7f9f2b19aa9a8968e4bf1c.jpg", "E:\\图片\\OpenCV\\191cc8dfb45229dbfbe02e8e035f61997a4c460e.jpg", "E:\\图片\\OpenCV\\194cde40aa186c709afd207c39b29027e9ab10e3.jpg", "E:\\图片\\OpenCV\\2.png", "E:\\图片\\OpenCV\\2117495106139bf9cbfc0935403d6e2bfff0913f.jpg", "E:\\图片\\OpenCV\\220px-ISBN_Details.svg.png", "E:\\图片\\OpenCV\\2f1b24348e35811ae06c03b9e0167682816213e3.jpg", "E:\\图片\\OpenCV\\2f2c99b07657d930cc00d457daf4f9662846d15a.jpg", "E:\\图片\\OpenCV\\3.png", "E:\\图片\\OpenCV\\30559b0b6b41edb1dc92ec75c86599becd62c232.jpg", "E:\\图片\\OpenCV\\3208351330d773b86df478adb1364ee04f894981.jpg", "E:\\图片\\OpenCV\\4.png", "E:\\图片\\OpenCV\\458830357a824c7724610692c5bfd9699f7180c7.jpg", "E:\\图片\\OpenCV\\4705130edb617b6795b8aafbba5078dad4579a70.jpg", "E:\\图片\\OpenCV\\5.png", "E:\\图片\\OpenCV\\5497833982f2e56d1fb3a6ca8fe0bc0a7f623c0e.jpg", "E:\\图片\\OpenCV\\5afc4288c21019904abfa2375d320e46c2e2e1b9.jpg", "E:\\图片\\OpenCV\\6.png", "E:\\图片\\OpenCV\\69802319bd98b8f7de9b78387f1126f470516289.jpg", "E:\\图片\\OpenCV\\7.png", "E:\\图片\\OpenCV\\8.png", "E:\\图片\\OpenCV\\80194dd44656342c9307d3b6067f005525ca8063.jpg", "E:\\图片\\OpenCV\\8397833588aa964ed7aad3003cec8628469427f4.jpg", "E:\\图片\\OpenCV\\9.png", "E:\\图片\\OpenCV\\9446ca016263a69c122d121706bfbb20126ad7cf.jpg", "E:\\图片\\OpenCV\\9649c7cddd8b91260bc36b97a58a82b29a04cfff.jpg", "E:\\图片\\OpenCV\\9778f67762ef6d0fcfcb33d1bca78f0d2c53e1ba.jpg", "E:\\图片\\OpenCV\\airplane.jpg", "E:\\图片\\OpenCV\\ajit_doval.jpg", "E:\\图片\\OpenCV\\aligned.jpg", "E:\\图片\\OpenCV\\amanda_bynes.jpg", "E:\\图片\\OpenCV\\angle_class_example.jpg", "E:\\图片\\OpenCV\\Backimage1.jpg", "E:\\图片\\OpenCV\\bali-crop.jpg", "E:\\图片\\OpenCV\\barcode1.jpg", "E:\\图片\\OpenCV\\barcode2.png", "E:\\图片\\OpenCV\\barcode3.jpg", "E:\\图片\\OpenCV\\barcode4.jpg", "E:\\图片\\OpenCV\\barcode5.jpg", "E:\\图片\\OpenCV\\bargh.png", "E:\\图片\\OpenCV\\BigFourSummerHeat.jpg", "E:\\图片\\OpenCV\\BisonBadlandsChillin.jpg", "E:\\图片\\OpenCV\\book1.jpg", "E:\\图片\\OpenCV\\book2.jpg", "E:\\图片\\OpenCV\\b_code128.png", "E:\\图片\\OpenCV\\b_code39.png", "E:\\图片\\OpenCV\\b_ean13.png", "E:\\图片\\OpenCV\\b_ean8.png", "E:\\图片\\OpenCV\\b_upca.png", "E:\\图片\\OpenCV\\b_upce.png", "E:\\图片\\OpenCV\\camel.jpg", "E:\\图片\\OpenCV\\Car.jpg", "E:\\图片\\OpenCV\\cards.jpg", "E:\\图片\\OpenCV\\cars.jpg", "E:\\图片\\OpenCV\\CASIA_0.jpg", "E:\\图片\\OpenCV\\cat-yawning-1765832.jpg", "E:\\图片\\OpenCV\\cat.jpg", "E:\\图片\\OpenCV\\CB_zps9975aa43.jpg", "E:\\图片\\OpenCV\\ch_doc1.jpg", "E:\\图片\\OpenCV\\ch_doc3.jpg", "E:\\图片\\OpenCV\\cmb_demo.jpg", "E:\\图片\\OpenCV\\Code128test-CodeB.png", "E:\\图片\\OpenCV\\Code128test-CodeC.png", "E:\\图片\\OpenCV\\ColumbiaRiverGorge.jpg", "E:\\图片\\OpenCV\\computer-vision.jpg", "E:\\图片\\OpenCV\\comwins.jpg", "E:\\图片\\OpenCV\\controls-01.jpg", "E:\\图片\\OpenCV\\corner-outside-frame.jpg", "E:\\图片\\OpenCV\\corridor.jpg", "E:\\图片\\OpenCV\\couple1.jpg", "E:\\图片\\OpenCV\\cow.jpg", "E:\\图片\\OpenCV\\crouch11-01.jpg", "E:\\图片\\OpenCV\\cube_b.JPG", "E:\\图片\\OpenCV\\cube_d.JPG", "E:\\图片\\OpenCV\\cube_f.JPG", "E:\\图片\\OpenCV\\cube_l.JPG", "E:\\图片\\OpenCV\\cube_r.JPG", "E:\\图片\\OpenCV\\cvlbl.png", "E:\\图片\\OpenCV\\cvmorph.png", "E:\\图片\\OpenCV\\dest.jpg", "E:\\图片\\OpenCV\\dog.jpg", "E:\\图片\\OpenCV\\EAN-13-ISBN-13.png", "E:\\图片\\OpenCV\\fruits.jpg", "E:\\图片\\OpenCV\\gass1.jpg", "E:\\图片\\OpenCV\\ghabz.jpg", "E:\\图片\\OpenCV\\ghabz01.jpg", "E:\\图片\\OpenCV\\ghabz01_resize.jpg", "E:\\图片\\OpenCV\\ghabz02.jpg", "E:\\图片\\OpenCV\\ghabz03.jpg", "E:\\图片\\OpenCV\\ghabz04.jpg", "E:\\图片\\OpenCV\\ghabz05.jpg", "E:\\图片\\OpenCV\\ghabz_bar01_128C.png", "E:\\图片\\OpenCV\\ghabz_bar02.png", "E:\\图片\\OpenCV\\ghabz_bar03.png", "E:\\图片\\OpenCV\\GiantSlabInOregon.jpg", "E:\\图片\\OpenCV\\GrandTetons.jpg", "E:\\图片\\OpenCV\\HERO.jpg", "E:\\图片\\OpenCV\\horses.jpg", "E:\\图片\\OpenCV\\howto_wpf_3d_stellate_geodesic_2.jpg", "E:\\图片\\OpenCV\\image.png", "E:\\图片\\OpenCV\\Image1.jpg", "E:\\图片\\OpenCV\\Image2.jpg", "E:\\图片\\OpenCV\\Image3.jpg", "E:\\图片\\OpenCV\\Image4.jpg", "E:\\图片\\OpenCV\\Image5.jpg", "E:\\图片\\OpenCV\\Image6.jpg", "E:\\图片\\OpenCV\\Image7.jpg", "E:\\图片\\OpenCV\\Image8.jpg", "E:\\图片\\OpenCV\\ImageExPlaceholder.jpg", "E:\\图片\\OpenCV\\Img.jpg", "E:\\图片\\OpenCV\\isbn.png", "E:\\图片\\OpenCV\\J.png", "E:\\图片\\OpenCV\\kite.jpg", "E:\\图片\\OpenCV\\LakeAnnMushroom.jpg", "E:\\图片\\OpenCV\\left.png", "E:\\图片\\OpenCV\\left01.jpg", "E:\\图片\\OpenCV\\left02.jpg", "E:\\图片\\OpenCV\\left03.jpg", "E:\\图片\\OpenCV\\left04.jpg", "E:\\图片\\OpenCV\\left05.jpg", "E:\\图片\\OpenCV\\left06.jpg", "E:\\图片\\OpenCV\\left07.jpg", "E:\\图片\\OpenCV\\left08.jpg", "E:\\图片\\OpenCV\\left09.jpg", "E:\\图片\\OpenCV\\left10.jpg", "E:\\图片\\OpenCV\\left11.jpg", "E:\\图片\\OpenCV\\left12.jpg", "E:\\图片\\OpenCV\\left13.jpg", "E:\\图片\\OpenCV\\LunchBreak.jpg", "E:\\图片\\OpenCV\\mdGcLHe.jpg", "E:\\图片\\OpenCV\\MilkyWayStHelensHikePurple.jpg", "E:\\图片\\OpenCV\\MitchellButtes.jpg", "E:\\图片\\OpenCV\\msbooklet.png", "E:\\图片\\OpenCV\\MultnomahFalls.jpg", "E:\\图片\\OpenCV\\newspaper.jpg", "E:\\图片\\OpenCV\\newspaper2.jpg", "E:\\图片\\OpenCV\\NorthernCascadesReflection.jpg", "E:\\图片\\OpenCV\\NovemberHikeWaterfall.jpg", "E:\\图片\\OpenCV\\ocv02.jpg", "E:\\图片\\OpenCV\\OIP.xI9MTXkpsp09YrwHKEVZ1gHaE8.jpg", "E:\\图片\\OpenCV\\OregonWineryNamaste.jpg", "E:\\图片\\OpenCV\\out_b076ad266891d7aa.jpg", "E:\\图片\\OpenCV\\out_b1096bc91a89b0cf.jpg", "E:\\图片\\OpenCV\\out_b8a3f2ea385e45b3.jpg", "E:\\图片\\OpenCV\\out_c91ee912164d8ecb.jpg", "E:\\图片\\OpenCV\\out_f194eaf4f3d1d835.jpg", "E:\\图片\\OpenCV\\Owl.jpg", "E:\\图片\\OpenCV\\Page6.jpg", "E:\\图片\\OpenCV\\PaintedHillsPathway.jpg", "E:\\图片\\OpenCV\\panda.jpg", "E:\\图片\\OpenCV\\Penguin.png", "E:\\图片\\OpenCV\\plane.jpg", "E:\\图片\\OpenCV\\pluto.jpg", "E:\\图片\\OpenCV\\right.png", "E:\\图片\\OpenCV\\right01.jpg", "E:\\图片\\OpenCV\\right02.jpg", "E:\\图片\\OpenCV\\right03.jpg", "E:\\图片\\OpenCV\\right04.jpg", "E:\\图片\\OpenCV\\right05.jpg", "E:\\图片\\OpenCV\\right06.jpg", "E:\\图片\\OpenCV\\right07.jpg", "E:\\图片\\OpenCV\\right08.jpg", "E:\\图片\\OpenCV\\right09.jpg", "E:\\图片\\OpenCV\\right10.jpg", "E:\\图片\\OpenCV\\right11.jpg", "E:\\图片\\OpenCV\\right12.jpg", "E:\\图片\\OpenCV\\right13.jpg", "E:\\图片\\OpenCV\\rightsideup-upsidedown.png", "E:\\图片\\OpenCV\\rotated-90degrees.png", "E:\\图片\\OpenCV\\RunningDogPacificCity.jpg", "E:\\图片\\OpenCV\\sample1.png", "E:\\图片\\OpenCV\\Sample_PDF417.png", "E:\\图片\\OpenCV\\ShootingOnAutoOnTheDrone.jpg", "E:\\图片\\OpenCV\\SmithnRockDownTheRiverView.jpg", "E:\\图片\\OpenCV\\SnowyInterbayt.jpg", "E:\\图片\\OpenCV\\SpeedTripleAtristsPoint.jpg", "E:\\图片\\OpenCV\\test.jpg", "E:\\图片\\OpenCV\\Van.jpg", "E:\\图片\\OpenCV\\vott-json-export\\0.jpg", "E:\\图片\\OpenCV\\vott-json-export\\000001.jpg", "E:\\图片\\OpenCV\\vott-json-export\\000131.jpg", "E:\\图片\\OpenCV\\vott-json-export\\000132.jpg", "E:\\图片\\OpenCV\\vott-json-export\\000133.jpg", "E:\\图片\\OpenCV\\vott-json-export\\000135.jpg", "E:\\图片\\OpenCV\\vott-json-export\\000136.jpg", "E:\\图片\\OpenCV\\vott-json-export\\000139.jpg", "E:\\图片\\OpenCV\\vott-json-export\\000140.jpg", "E:\\图片\\OpenCV\\vott-json-export\\000141.jpg", "E:\\图片\\OpenCV\\vott-json-export\\000142.jpg", "E:\\图片\\OpenCV\\VoTT.jpg", "E:\\图片\\OpenCV\\WestSeattleView.jpg", "E:\\图片\\OpenCV\\woman.jpg", "E:\\图片\\OpenCV\\yolov6-inference-cycling.jpg", "E:\\图片\\OpenCV\\yolov6-inference-elephants.jpg", "E:\\图片\\OpenCV\\yolov6-inference-soccer.jpg", "E:\\图片\\OpenCV\\yolov6-inference-traffic.jpg", "E:\\图片\\OpenCV\\youwin.jpg", "E:\\图片\\OpenCV\\zbar-test.jpg", "E:\\图片\\OpenCV\\zh_val_40.jpg", "E:\\图片\\OpenCV\\zidane.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JR6ADWF.info\\0.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JR6ADWF.info\\0_thumbnail.png", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JS12PEO.info\\2.png", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JS1EJCY.info\\000183.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JS1XL1H.info\\000158.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JS2JBO1.info\\000139.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JS2PQHD.info\\000140.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JS3LB2V.info\\000197.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JS58UY3.info\\177ac23c1e427c46e059c156fe8421e46afc7e0f.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JS5E8UY.info\\5afc4288c21019904abfa2375d320e46c2e2e1b9.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JS62B7Z.info\\000135.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JS6GV1A.info\\128C.png", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JS6OZNI.info\\0.png", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JS6XYEH.info\\188a91e594abfc117a7f9f2b19aa9a8968e4bf1c.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JS7A4V1.info\\177f46fa63c4951fe783ad2880f21aa84119ef83.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JS87UH7.info\\000161.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JS8O7OK.info\\000182.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JS8PTDB.info\\000165.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JS8RKA4.info\\000150.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JS8RV1M.info\\148c405072f9d5501bcb89c99f2667ceb75d6258.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JS93B7S.info\\000164.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JS94OUX.info\\000178.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JS99SVN.info\\000175.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JS9E895.info\\191cc8dfb45229dbfbe02e8e035f61997a4c460e.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JS9GXL2.info\\2f1b24348e35811ae06c03b9e0167682816213e3.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JS9NX8I.info\\00.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JSA4UOX.info\\181d4e43d7586f7b20465c796372c56f3a818627.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JSASFQJ.info\\185c0e326b2eb32bac82b157a8fe4eb7cbc0cdee.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JSB85M1.info\\000176.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JSBEC6H.info\\000147.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JSBJYI4.info\\000198.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JSBPQ64.info\\000174.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JSCR1P5.info\\000191.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JSCXDPV.info\\000188.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JSD8FB6.info\\000152.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JSDN4FB.info\\7.png", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JSDRXMM.info\\000155.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JSDVP15.info\\6.png", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JSE2X7W.info\\3.png", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JSEQXRG.info\\000186.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JSF30JE.info\\000171.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JSFAJM3.info\\01.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JSFR2BX.info\\000132.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JSG8POM.info\\000194.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JSGMAPB.info\\000131.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JSHIYGT.info\\000193.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JSHLP02.info\\000156.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JSHNDG8.info\\8.png", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JSHNRR2.info\\000163.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JSI5YG2.info\\2f2c99b07657d930cc00d457daf4f9662846d15a.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JSI6V83.info\\000136.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JSI7YTC.info\\000141.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JSII60Q.info\\000172.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JSJFXL8.info\\186b2bc2fe09d2991566f400ec8404d096c03a0e.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JSJK4XC.info\\000173.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JSJLGWM.info\\000180.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JSK1BDO.info\\194cde40aa186c709afd207c39b29027e9ab10e3.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JSK8LZ8.info\\1.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JSL0CFV.info\\000192.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JSL70KY.info\\17-license-plate.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JSLBRZJ.info\\134e302e4b799d631591ab22db6ec0b47b8134f1.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JSN9BDZ.info\\000153.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JSNHZZD.info\\000157.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JSOZ9BL.info\\000151.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JSP391L.info\\167afe6d1f7406de29b43f8bc2e71090c1e357e6.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JSPJ56L.info\\1.png", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JSRHNV8.info\\000187.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JSRMR3K.info\\000170.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JSRTNHP.info\\9.png", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JSSD4I7.info\\02.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JST276A.info\\4.png", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JST9FSZ.info\\000143.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JSTIOXB.info\\000190.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JSTK7FT.info\\000196.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JSUJ6CF.info\\000179.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JSUWT4E.info\\000133.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JSW57RA.info\\000145.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JSW6N5D.info\\000154.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JSXFKP7.info\\000142.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JSXK6CH.info\\000160.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JSXT1KN.info\\5.png", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JSXTUYJ.info\\000185.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JSXUSD2.info\\000001.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JSYD0X0.info\\000162.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JSZVXV9.info\\153aacbc959e57d1b4e8f8642ad080e7ab23a681.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JSZYY25.info\\000166.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JT0A06T.info\\220px-ISBN_Details.svg.png", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JT1110U.info\\009414.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JT11CM4.info\\009997.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JT1VLGL.info\\009412.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JT2QK4P.info\\009426.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JT2TUKW.info\\69802319bd98b8f7de9b78387f1126f470516289.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JT32JXR.info\\00059985.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JT38LAS.info\\9446ca016263a69c122d121706bfbb20126ad7cf.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JT3MPXQ.info\\009435.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JT4NCS5.info\\009432.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JT4NQ8L.info\\009415.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JT4VIDJ.info\\009995.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JT5LCH0.info\\00077949.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JT61OSQ.info\\009403.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JT6OHAN.info\\3208351330d773b86df478adb1364ee04f894981.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JT78HPE.info\\009400.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JT7GKL7.info\\009441.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JT8V7OE.info\\009395.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JT9L8T5.info\\009409.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTAJWDX.info\\009447.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTANF97.info\\009402.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTAP0E0.info\\30559b0b6b41edb1dc92ec75c86599becd62c232.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTBD5XX.info\\009453.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTBEFN6.info\\009438.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTBRDF4.info\\009456.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTBRYFJ.info\\000200.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTBVZX0.info\\009391.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTBYTL8.info\\458830357a824c7724610692c5bfd9699f7180c7.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTCEMB2.info\\009396.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTD32JT.info\\009399.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTDGGSA.info\\009417.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTDGWQJ.info\\009394.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTE3Y9P.info\\5497833982f2e56d1fb3a6ca8fe0bc0a7f623c0e.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTEFJ7K.info\\00056221.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTFMVJ8.info\\2117495106139bf9cbfc0935403d6e2bfff0913f.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTFSKXH.info\\009442.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTFWR2L.info\\009419.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTGCQKE.info\\009405.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTGVF93.info\\009445.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTH856P.info\\009392.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTHB99W.info\\009431.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTHRG9J.info\\009460.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTI305H.info\\009434.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTJGMCI.info\\00207393.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTJL93C.info\\00111002.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTJLUIV.info\\4705130edb617b6795b8aafbba5078dad4579a70.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTJWFJV.info\\009428.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTK87YI.info\\009458.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTKCGJW.info\\009408.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTKE2ZB.info\\009440.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTKKZ72.info\\012166efd5b0c20dbf846c713af257a78251e2f3.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTKV0QW.info\\009422.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTL4708.info\\009457.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTLGZAX.info\\9649c7cddd8b91260bc36b97a58a82b29a04cfff.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTLW32I.info\\009393.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTN1BGU.info\\009407.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTN7YYZ.info\\009420.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTNCSDH.info\\009450.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTNPANG.info\\009452.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTO6JEL.info\\009410.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTO9JZD.info\\009437.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTOL423.info\\009427.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTORGUS.info\\00057937.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTORGUS.info\\00057937_thumbnail.png", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTP40LN.info\\009996.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTP6GDZ.info\\009448.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTPA84R.info\\009439.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTPXNZN.info\\009421.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTQIC4L.info\\009444.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTQRTXG.info\\9778f67762ef6d0fcfcb33d1bca78f0d2c53e1ba.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTR1F95.info\\149521354b3e974148ef68fc745060f78313e3c7.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTR29XS.info\\16562069787_a53a8cdccf_n.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTR3NUX.info\\009451.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTRWD4U.info\\009425.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTS44BP.info\\009418.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTSM236.info\\009433.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTSSSBA.info\\009416.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTT1T17.info\\009401.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTT77ZY.info\\009430.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTTJJLX.info\\009443.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTTKVWV.info\\009429.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTU03MA.info\\009461.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTU7JW6.info\\009436.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTUQKD1.info\\009459.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTVALX2.info\\009998.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTWFTYZ.info\\8397833588aa964ed7aad3003cec8628469427f4.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTX1E0X.info\\009423.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTX21CV.info\\80194dd44656342c9307d3b6067f005525ca8063.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTXRE20.info\\009999.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTXVEUX.info\\009455.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JTYPORI.info\\009454.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JU08IDM.info\\crouch11-01.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JU08IDM.info\\crouch11-01_thumbnail.png", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JU0FGRP.info\\right13.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JU0JS47.info\\mdGcLHe.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JU0K9L8.info\\GrandTetons.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JU0P1LK.info\\newspaper2.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JU0YQA9.info\\甘A76L77.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JU1DLV8.info\\甘A0E460.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JU1FI6W.info\\cat-yawning-1765832.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JU1FI6W.info\\cat-yawning-1765832_thumbnail.png", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JU1Z64K.info\\甘ASG986.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JU2CFD6.info\\SpeedTripleAtristsPoint.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JU2HI6K.info\\corridor.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JU2WRXS.info\\OregonWineryNamaste.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JU37AUD.info\\J.png", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JU3A7PG.info\\ajit_doval.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JU3J3AZ.info\\GiantSlabInOregon.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JU3SNC2.info\\bargh.png", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JU3ZDBU.info\\left01.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JU48V7G.info\\甘A818L7.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JU4I8F8.info\\angle_class_example.png", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JU4IYAB.info\\right10.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JU4JFHZ.info\\couple1.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JU4OV6D.info\\barcode1.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JU50513.info\\ghabz_bar03.png", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JU5I8YT.info\\image.png", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JU5NRJI.info\\WestSeattleView.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JU6JY7U.info\\Backimage1.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JU6MP80.info\\NovemberHikeWaterfall.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JU6OSHQ.info\\barcode3.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JU6S1SQ.info\\right.png", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JU6VO0F.info\\甘AP2999.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JU6YKH0.info\\dog.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JU70003.info\\airplane.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JU76QQQ.info\\left03.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JU778JZ.info\\ghabz04.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JU78GIY.info\\right05.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JU7DXB5.info\\甘A85988.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JU7G6DQ.info\\cube_f.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JU7KVAZ.info\\cards.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JU7TQO8.info\\Image6.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JU887ZY.info\\Image8.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JU8AVB1.info\\VoTT.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JU8AVB1.info\\VoTT_thumbnail.png", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JU8DHFN.info\\cube_b.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JU8ML1X.info\\甘A076A6.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JU8O1XH.info\\ghabz05.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JU8VYE0.info\\LakeAnnMushroom.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JU931FZ.info\\ocv02.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JU954EC.info\\b_ean8.png", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JU9G0T6.info\\b_code39.png", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JU9H2VR.info\\甘AW9238.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JU9QFCO.info\\ghabz_bar02.png", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUA0P2W.info\\Page6.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUA0P2W.info\\Page6_thumbnail.png", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUA79AE.info\\plane.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUAEA8H.info\\MultnomahFalls.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUAJ0X5.info\\Van.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUAL0AO.info\\left.png", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUB2D5Q.info\\甘AB6K71.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUBAND8.info\\right09.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUBC9T0.info\\b_code128.png", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUBD0TQ.info\\甘A9T528.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUBP9L6.info\\甘AB0Z80.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUBRAO6.info\\BisonBadlandsChillin.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUC3FC4.info\\rotated-90degrees.png", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUC7RFZ.info\\甘A096R2.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUCCX0K.info\\甘A17M35.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUCFCJJ.info\\out_b076ad266891d7aa.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUCT1MX.info\\corner-outside-frame.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUCT1MX.info\\corner-outside-frame_thumbnail.png", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUDCCST.info\\Image2.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUDPFHK.info\\barcode4.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUDQ419.info\\computer-vision.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUDQ419.info\\computer-vision_thumbnail.png", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUDQ7VT.info\\cube_l.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUE00W4.info\\Penguin.png", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUE0GF6.info\\out_b8a3f2ea385e45b3.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUEXXXH.info\\甘AT8A70.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUEY8MV.info\\cmb_demo.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUEYNDB.info\\甘A910R2.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUF549I.info\\b_ean13.png", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUF8HV0.info\\camel.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUF8HV0.info\\camel_thumbnail.png", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUFBT4S.info\\b_upce.png", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUFCDL6.info\\PaintedHillsPathway.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUFHKR7.info\\left07.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUFKTVZ.info\\msbooklet.png", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUFWDDO.info\\OIP.xI9MTXkpsp09YrwHKEVZ1gHaE8.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUFYVZ5.info\\cvmorph.png", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUG0E66.info\\SnowyInterbayt.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUG0U5A.info\\甘AQ1M78.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUGET29.info\\pluto.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUHAAFJ.info\\rightsideup-upsidedown.png", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUHBV50.info\\BigFourSummerHeat.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUHVCHE.info\\ch_doc1.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUHYK2L.info\\left05.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUI9YWS.info\\dest.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUIET0E.info\\sample1.png", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUIIJSV.info\\ghabz01.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUIIJSV.info\\ghabz01_thumbnail.png", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUIUGF4.info\\newspaper.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUIX721.info\\HERO.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUIX721.info\\HERO_thumbnail.png", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUJ5TVQ.info\\right11.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUJEGDM.info\\left09.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUJG48S.info\\yolov6-inference-cycling.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUK4RI7.info\\left08.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUKBA5M.info\\Code128test-CodeB.png", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUKJ0Q6.info\\book1.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUKJ0Q6.info\\book1_thumbnail.png", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUKMBRH.info\\test.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUKWEBG.info\\right02.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUL00ND.info\\甘A660Q6.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUL2UHM.info\\ghabz.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JULFE20.info\\ch_doc3.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JULGZ7Y.info\\Image4.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JULIKEA.info\\Img.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JULRM7L.info\\cube_r.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JULV5NJ.info\\RunningDogPacificCity.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JULVF4P.info\\甘ADS580.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUM1E21.info\\left10.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUMD8Q7.info\\right01.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUMVXAO.info\\cube_d.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUN1T0A.info\\woman.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUN2S19.info\\Sample_PDF417.png", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUN5D98.info\\right07.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUN9H9J.info\\left13.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUNN49V.info\\Image3.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUNSHQ7.info\\MilkyWayStHelensHikePurple.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUO1UY3.info\\NorthernCascadesReflection.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUOCKEB.info\\ghabz01_resize.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUOJTWZ.info\\Code128test-CodeC.png", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUOJTWZ.info\\Code128test-CodeC_thumbnail.png", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUOW7IZ.info\\left02.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUOYN9F.info\\amanda_bynes.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUPMMY4.info\\甘A497D9.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUPNFUX.info\\ghabz02.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUPQ0QL.info\\isbn.png", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUPRQQO.info\\left12.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUQ0VHJ.info\\cat.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUQ6TDM.info\\SmithnRockDownTheRiverView.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUQ8YGA.info\\kite.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUQ8YGA.info\\kite_thumbnail.png", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUQDDXW.info\\ghabz_bar01_128C.png", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUQETHY.info\\Image5.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUQPHOF.info\\bali-crop.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUQRUZ7.info\\book2.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUQRUZ7.info\\book2_thumbnail.png", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUQYDI4.info\\fruits.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUR644E.info\\barcode2.png", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUR9ZM1.info\\left04.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUS7YL2.info\\ghabz03.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUS9J3G.info\\gass1.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUSFAG2.info\\甘A815L7.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUSJ1FC.info\\out_c91ee912164d8ecb.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUSV1B0.info\\甘AE7F87.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUSYMED.info\\MitchellButtes.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUT0WEX.info\\LunchBreak.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUT4DHB.info\\comwins.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUTPOUG.info\\horses.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUTWCJ1.info\\howto_wpf_3d_stellate_geodesic_2.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUU5XCD.info\\ImageExPlaceholder.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUU902P.info\\CB_zps9975aa43.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUUBLIQ.info\\right08.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUUE025.info\\cars.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUUE025.info\\cars_thumbnail.png", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUUET35.info\\CASIA_0.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUUF5GS.info\\ColumbiaRiverGorge.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUUQ9KA.info\\甘A17J18.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUUSGSQ.info\\Image7.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUUTK6O.info\\甘A789B6.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUV7054.info\\甘ANS526.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUVL1ES.info\\out_f194eaf4f3d1d835.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUVOAB9.info\\left06.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUVQ6GZ.info\\cvlbl.png", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUVYLGT.info\\out_b1096bc91a89b0cf.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUW44OA.info\\barcode5.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUWEYTC.info\\panda.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUX1XWB.info\\甘AL3V97.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUX4HKE.info\\right04.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUXO8XK.info\\right03.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUXWNSG.info\\b_upca.png", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUYDI5Z.info\\controls-01.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUYDI5Z.info\\controls-01_thumbnail.png", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUYQN8Z.info\\right12.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUYSWL6.info\\aligned.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUYXJNR.info\\Owl.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUYXT3U.info\\甘AXP299.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUZ6XTC.info\\right06.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUZ8BX6.info\\Car.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUZGAEG.info\\EAN-13-ISBN-13.png", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUZKMXS.info\\ShootingOnAutoOnTheDrone.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUZLR5X.info\\cow.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JUZX54E.info\\left11.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JV3V0KA.info\\yolov6-inference-traffic.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JV5MKHF.info\\zidane.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JV6040M.info\\youwin.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JVHKXT1.info\\zbar-test.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JVKZQ5N.info\\yolov6-inference-soccer.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JVVTMQY.info\\zh_val_40.jpg", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JVVTMQY.info\\zh_val_40_thumbnail.png", "E:\\图片\\OpenCV\\测试.library\\images\\LAUGP5JVWEIP3.info\\yolov6-inference-elephants.jpg", "E:\\图片\\OpenCV\\甘A076A6.jpg", "E:\\图片\\OpenCV\\甘A096R2 (1).jpg", "E:\\图片\\OpenCV\\甘A096R2.jpg", "E:\\图片\\OpenCV\\甘A0E460.jpg", "E:\\图片\\OpenCV\\甘A17J18 (1).jpg", "E:\\图片\\OpenCV\\甘A17J18.jpg", "E:\\图片\\OpenCV\\甘A17M35.jpg", "E:\\图片\\OpenCV\\甘A497D9.jpg", "E:\\图片\\OpenCV\\甘A660Q6.jpg", "E:\\图片\\OpenCV\\甘A76L77.jpg", "E:\\图片\\OpenCV\\甘A789B6.jpg", "E:\\图片\\OpenCV\\甘A815L7.jpg", "E:\\图片\\OpenCV\\甘A818L7.jpg", "E:\\图片\\OpenCV\\甘A85988.jpg", "E:\\图片\\OpenCV\\甘A910R2.jpg", "E:\\图片\\OpenCV\\甘A9T528.jpg", "E:\\图片\\OpenCV\\甘AB0Z80.jpg", "E:\\图片\\OpenCV\\甘AB6K71.jpg", "E:\\图片\\OpenCV\\甘ADS580.jpg", "E:\\图片\\OpenCV\\甘AE7F87.jpg", "E:\\图片\\OpenCV\\甘AL3V97.jpg", "E:\\图片\\OpenCV\\甘ANS526.jpg", "E:\\图片\\OpenCV\\甘AP2999.jpg", "E:\\图片\\OpenCV\\甘AQ1M78.jpg", "E:\\图片\\OpenCV\\甘ASG986 (1).jpg", "E:\\图片\\OpenCV\\甘ASG986.jpg", "E:\\图片\\OpenCV\\甘AT8A70 (1).jpg", "E:\\图片\\OpenCV\\甘AT8A70.jpg", "E:\\图片\\OpenCV\\甘AW9238.jpg", "E:\\图片\\OpenCV\\甘AXP299 (1).jpg", "E:\\图片\\OpenCV\\甘AXP299.jpg"]}, "ROI": {"$id": "13", "$type": "H.VisionMaster.NodeData.ROIPresenters.FromROI, H.VisionMaster.NodeData", "ID": "dc8d7366-3257-43d2-b898-8f51639e5877", "Name": "继承"}, "FromROI": {"$ref": "13"}, "DrawROI": {"$id": "14", "$type": "H.VisionMaster.NodeData.ROIPresenters.DrawROI, H.VisionMaster.NodeData", "Rect": "Empty", "ID": "b5ade289-b48e-461d-82b7-a9f5be2f422d", "Name": "绘制"}, "InputROI": {"$id": "15", "$type": "H.VisionMaster.NodeData.ROIPresenters.InputROI, H.VisionMaster.NodeData", "ID": "264cf327-a34b-4647-b0bb-6355707ec320", "Name": "输入"}, "PreviewMillisecondsDelay": 0, "InvokeMillisecondsDelay": 0, "FlagLength": 10.0, "State": "Success", "TimeSpan": "00:00:00.0066354", "Message": "运行成功", "DiagramData": {"$ref": "1"}, "Text": "人物图像源", "FontSize": 12.0, "PortDatas": {"$type": "System.Collections.Generic.List`1[[H.Controls.Diagram.Datas.IPortData, H.Controls.Diagram]], System.Private.CoreLib", "$values": [{"$id": "16", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "State": "Wait", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Top", "NodeID": "f8ed7d14-32de-4d4d-a15b-1a22d75fa8b9", "PortType": "Input", "ID": "f63044d1-d5fc-4789-9b9d-09f1df849818"}, {"$id": "17", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "State": "Wait", "Message": "运行成功", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Bottom", "NodeID": "f8ed7d14-32de-4d4d-a15b-1a22d75fa8b9", "PortType": "OutPut", "ID": "e7585290-edc3-4db9-a70f-6e760c7d3f71"}, {"$id": "18", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "State": "Wait", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "NodeID": "f8ed7d14-32de-4d4d-a15b-1a22d75fa8b9", "PortType": "Input", "ID": "4b6f97ac-1514-452a-ae37-2a2e267a561c"}, {"$id": "19", "$type": "H.VisionMaster.NodeData.Base.StyleNodeDataBase+OpenCVFlowablePortData, H.VisionMaster.NodeData", "State": "Wait", "FontSize": 12.0, "Fill": "#FFFFFFFF", "StrokeThickness": 1.0, "Width": 6.0, "Height": 6.0, "Dock": "Right", "NodeID": "f8ed7d14-32de-4d4d-a15b-1a22d75fa8b9", "PortType": "OutPut", "ID": "d94cf36a-ce22-4a59-abb9-0b846d776497"}]}, "IsTemplate": false, "Height": 35.0, "Width": 120.0, "Fill": "#FFFFFFFF", "CornerRadius": 2.0, "Location": "521.0849315068493,592.4657534246572", "ID": "f8ed7d14-32de-4d4d-a15b-1a22d75fa8b9", "Name": "人物图像源", "Icon": ""}]}, "LinkDatas": {"$type": "System.Collections.Generic.List`1[[H.Controls.Diagram.Datas.ILinkData, H.Controls.Diagram]], System.Private.CoreLib", "$values": [{"$id": "20", "$type": "H.Controls.Diagram.Presenter.LinkDatas.FlowableLinkData, H.Controls.Diagram.Presenter", "State": "Wait", "FontSize": 12.0, "Stroke": "#FFFFA500", "FromNodeID": "f8ed7d14-32de-4d4d-a15b-1a22d75fa8b9", "ToNodeID": "f694edb2-128b-40f9-a7d8-06ee95ca4d0b", "FromPortID": "e7585290-edc3-4db9-a70f-6e760c7d3f71", "ToPortID": "494e53b1-2185-47b7-bf32-4d4a1abdc3c0", "ID": "1c71191c-9344-43f7-ba6e-d7a1d0d316fe", "Name": "连线"}]}}, "ID": "f6df7228-ede6-40ce-b096-7ca98998570c"}]}