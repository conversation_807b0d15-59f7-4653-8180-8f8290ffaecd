# WPF-VisionMaster 技术架构分析与二次开发指南

## 📋 项目概述

WPF-VisionMaster是一个基于WPF和OpenCV的机器视觉处理软件，采用插件化架构设计，支持可视化编程和模块化扩展。项目使用.NET 8.0框架，集成了OpenCVSharp、ONNX等先进技术栈。

### 🏗️ 整体架构

#### 解决方案结构
```
WPF-VisionMaster/
├── Solution/WPF-VisionMaster.sln    (37个项目统一管理)
├── Source/
│   ├── VisionMaster/                 (14个核心项目)
│   │   ├── H.VisionMaster.OpenCV     (OpenCV图像处理)
│   │   ├── H.VisionMaster.NodeData   (节点数据基础)
│   │   ├── H.VisionMaster.NodeGroup  (节点分组管理)
│   │   ├── H.VisionMaster.DiagramData(流程图数据)
│   │   ├── H.VisionMaster.Project    (项目管理)
│   │   └── H.VisionMaster.Network    (网络通信)
│   ├── WPF-Control/                  (23个控件库项目)
│   │   ├── Controls/                 (UI控件)
│   │   ├── Extensions/               (扩展功能)
│   │   ├── Modules/                  (功能模块)
│   │   ├── Services/                 (服务层)
│   │   └── Themes/                   (主题系统)
│   ├── Apps/                         (应用程序)
│   ├── NodeDatas/                    (节点数据)
│   └── Setups/                       (安装程序)
```

#### 技术栈
- **.NET 8.0** - 目标框架
- **WPF** - 用户界面框架
- **OpenCVSharp 4.6.0** - 图像处理库
- **ONNX Runtime** - 机器学习推理
- **NModbus** - 工业通信协议
- **Newtonsoft.Json** - JSON序列化

### 🔧 核心架构组件

#### 1. 应用启动框架 (ApplicationBase)
```csharp
protected override void OnStartup(StartupEventArgs e)
{
    this.Configure();           // 配置服务
    this.OnSingleton(e);       // 单例检查
    base.OnStartup(e);
    Window window = this.CreateMainWindow(e);  // 创建主窗口
    this.OnSplashScreen(e);    // 启动画面
    this.OnLogin();            // 登录处理
    this.MainWindow.Show();    // 显示主窗口
}
```

#### 2. 依赖注入系统 (IoC)
- **服务注册**: ConfigureServices方法注册所有依赖
- **服务构建**: Ioc.Build()构建服务提供者
- **服务获取**: Ioc.Get<T>()获取服务实例

#### 3. 节点执行引擎 (FlowableNodeData)
```csharp
public virtual async Task<bool?> Start(IFlowableDiagramData diagramData, IFlowableLinkData from = null)
{
    using (new PartDataInvokable(this, diagramData.OnInvokingPart, diagramData.OnInvokedPart))
    {
        nresult = await this.TryInvokeAsync(from, diagramData);
    }
    // 处理串行/并行执行模式
}
```

### 🎯 插件模块深度分析

#### 节点分组系统
- **SrcImages**: 图像源节点 (摄像头、文件、网络)
- **Preprocessings**: 预处理节点 (滤波、增强、变换)
- **Features**: 特征提取节点 (边缘、角点、轮廓)
- **Measurements**: 测量节点 (距离、角度、面积)
- **Classifications**: 分类节点 (ONNX模型、传统分类)
- **Results**: 结果输出节点 (显示、保存、通信)

#### 自动发现机制
```csharp
protected override IEnumerable<INodeData> CreateNodeDatas()
{
    return this.GetType().Assembly.GetInstances<ISrcImageGroupableNodeData>()
           .OrderBy(x => x.Order);
}
```

### 🔄 核心数据流分析

#### 1. 应用启动流程
1. **构造阶段**: 设置关闭模式 → 注册路径服务 → 初始化异常处理 → 构建IoC容器
2. **服务注册**: ConfigureServices注册所有依赖 → Ioc.Build构建服务提供者
3. **启动阶段**: Configure配置 → 单例检查 → 创建主窗口 → 启动画面加载 → 登录处理 → 显示主窗口

#### 2. 节点执行机制
- **Start** → **TryInvokeAsync** → **InvokeAsync/Invoke** → **结果传递**
- 支持串行/并行执行模式 (InvokeMode)
- 完整的异常处理和状态管理
- 通过IFlowableLinkData在节点间传递数据

#### 3. 状态管理体系
- **节点级**: Ready, Running, Success, Error, Break, Canceling
- **流程图级**: DiagramFlowableState管理整体执行状态
- **消息系统**: VisionMessage收集执行结果和时间统计

#### 4. 序列化与持久化
- **多格式支持**: JSON (Newtonsoft/System.Text.Json)、XML序列化
- **项目管理**: ProjectServiceBase提供完整的项目生命周期管理
- **模板系统**: DiagramTemplates支持流程图模板保存和复用
- **配置管理**: AppPaths统一管理各类配置文件路径

### 🚀 二次开发指南

#### 开发环境配置
**必需环境**:
- .NET 8.0 SDK (目标框架: net8.0-windows)
- Visual Studio 2022 或 Visual Studio Code
- Git 版本控制工具

**关键配置**:
- C# 语言版本: 10.0
- WPF 支持: 已启用 (UseWPF=true)
- 隐式引用: 已启用 (ImplicitUsings=enable)

#### 新节点开发步骤

**1. 创建节点类**:
```csharp
[Icon(FontIcons.YourIcon)]
[Display(Name = "节点名称", GroupName = "分组名", Description = "描述")]
public class YourNodeData : OpenCVNodeDataBase, IYourGroupableNodeData
{
    // 参数属性
    private double _parameter = 0.0;
    [PropertyItem(typeof(DoubleSliderTextPropertyItem))]
    [Range(0.0, 100.0)]
    [Display(Name = "参数名", GroupName = VisionPropertyGroupNames.RunParameters)]
    public double Parameter
    {
        get { return _parameter; }
        set { _parameter = value; RaisePropertyChanged(); this.UpdateInvokeCurrent(); }
    }

    // 核心处理逻辑
    protected override async Task<IFlowableResult> InvokeAsync(IFlowableLinkData from, IFlowableDiagramData diagramData)
    {
        Mat input = from?.GetMat();
        if (input == null) return this.GetErrorResult("输入图像为空");

        Mat output = new Mat();
        // OpenCV处理代码...

        return this.GetSuccessResult(output);
    }
}
```

**2. 注册到节点组**:
- 实现对应的分组接口 (如 IPreprocessingGroupableNodeData)
- 节点会自动被发现和注册

#### 插件扩展最佳实践

**依赖注入配置**:
```csharp
protected override void ConfigureServices(IServiceCollection services)
{
    base.ConfigureServices(services);
    services.AddApplicationServices();
    services.AddProject<YourProjectService>(x =>
    {
        x.Extenstion = ".json";
        x.JsonSerializerService = new NewtonsoftJsonSerializerService();
    });
}
```

#### 代码规范和架构约定

**命名规范**:
- 项目命名: `H.{模块}.{子模块}`
- 节点命名: 功能描述 + `NodeData`
- 接口命名: `I{功能}Groupable{类型}Data`

**架构模式**:
- **MVVM**: 严格遵循Model-View-ViewModel模式
- **依赖注入**: 使用内置IoC容器管理服务
- **异步编程**: 所有图像处理使用async/await
- **错误处理**: 统一使用IFlowableResult返回结果

#### 调试和测试方法

**编译项目**:
```bash
dotnet build Solution\WPF-VisionMaster.sln --configuration Release
```

**运行应用**:
```bash
dotnet run --project Source\Apps\H.App.VisionMaster.OpenCV
```

**调试技巧**:
- 使用 `IocLog.Instance?.Info()` 记录日志
- 在节点中设置断点调试处理逻辑
- 利用VisionMessage收集执行结果

### 📊 项目特色功能

#### 1. 可视化编程
- 拖拽式节点编辑器
- 实时预览和调试
- 流程图模板系统

#### 2. 模块化架构
- 插件式节点系统
- 自动服务发现
- 热插拔支持

#### 3. 企业级特性
- 完整的项目管理
- 多种序列化格式
- 工业通信协议支持
- 主题系统

### 🎯 快速开始

**最小节点模板**:
```csharp
[Display(Name = "示例节点", GroupName = "自定义")]
public class ExampleNodeData : OpenCVNodeDataBase, IPreprocessingGroupableNodeData
{
    protected override async Task<IFlowableResult> InvokeAsync(IFlowableLinkData from, IFlowableDiagramData diagramData)
    {
        Mat input = from?.GetMat();
        if (input == null) return this.GetErrorResult("无输入图像");
        
        Mat output = input.Clone();
        return this.GetSuccessResult(output);
    }
}
```

---

**编译状态**: ✅ 成功 (37个项目，130个警告)
**开发就绪**: 🚀 可以开始二次开发

这个强大的机器视觉框架为您提供了完整的开发基础，支持从简单的图像处理到复杂的AI推理应用。
