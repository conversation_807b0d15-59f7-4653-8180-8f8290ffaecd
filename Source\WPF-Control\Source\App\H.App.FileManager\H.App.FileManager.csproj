﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <UseWPF>true</UseWPF>
    <TargetFrameworks>net7.0-windows</TargetFrameworks>
    <ApplicationIcon>logo.ico</ApplicationIcon>
    <Product>H文件管理器</Product>
    <Description>电脑文件管理器：包括对文件的收藏、评分、打标签和按工程管理，支持多种预览方式（表格，缩率图，预览图等），对图片、视频支持预览，放大，缩小，对视频支持播放、截图、全屏播放、放大、缩小，定位到截图时间位置等，自定义收藏夹、标签夹</Description>
    <AssemblyVersion>1.0.0</AssemblyVersion>
    <FileVersion>1.0.0</FileVersion>
    <Copyright>Copyright © HeBianGu 2024-2024</Copyright>
    <Authors></Authors>
    <PackageProjectUrl>https://github.com/HeBianGu</PackageProjectUrl>    
    <Title>H文件管理器</Title>
    <Version>1.0.0</Version>
    <GeneratePackageOnBuild>Flase</GeneratePackageOnBuild>
    <Platforms>AnyCPU;x86</Platforms>
    <!--<SignAssembly>True</SignAssembly>-->
    <!--<AssemblyOriginatorKeyFile>H:\Github\WPF-Control\Key.snk</AssemblyOriginatorKeyFile>-->
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\ApplicationBases\H.ApplicationBases.Default\H.ApplicationBases.Default.csproj" />
    <ProjectReference Include="..\..\ApplicationBases\H.ApplicationBases.Identify\H.ApplicationBases.Identify.csproj" />
    <ProjectReference Include="..\..\Controls\H.Controls.FavoriteBox\H.Controls.FavoriteBox.csproj" />
    <ProjectReference Include="..\..\Controls\H.Controls.FilterBox\H.Controls.FilterBox.csproj" />
    <ProjectReference Include="..\..\Controls\H.Controls.GridSplitterBox\H.Controls.GridSplitterBox.csproj" />
    <ProjectReference Include="..\..\Controls\H.Controls.OrderBox\H.Controls.OrderBox.csproj" />
    <ProjectReference Include="..\..\Controls\H.Controls.OutlookBar\H.Controls.OutlookBar.csproj" />
    <ProjectReference Include="..\..\Controls\H.Controls.PagerBox\H.Controls.PagerBox.csproj" />
    <ProjectReference Include="..\..\Controls\H.Controls.PropertyGrid\H.Controls.PropertyGrid.csproj" />
    <ProjectReference Include="..\..\Controls\H.Controls.RepositoryBox\H.Controls.RepositoryBox.csproj" />
    <ProjectReference Include="..\..\Controls\H.Controls.TagBox\H.Controls.TagBox.csproj" />
    <ProjectReference Include="..\..\Controls\H.Controls.Vlc\H.Controls.Vlc.csproj" />
    <ProjectReference Include="..\..\Controls\H.Controls.ZoomBox\H.Controls.ZoomBox.csproj" />
    <ProjectReference Include="..\..\Extensions\H.Extensions.ApplicationBase\H.Extensions.ApplicationBase.csproj" />
    <ProjectReference Include="..\..\Extensions\H.Extensions.BackgroundImage\H.Extensions.BackgroundImage.csproj" />
    <ProjectReference Include="..\..\Extensions\H.Extensions.FFMpeg\H.Extensions.FFMpeg.csproj" />
    <ProjectReference Include="..\..\Extensions\H.Extensions.Torrent\H.Extensions.Torrent.csproj" />
    <ProjectReference Include="..\..\Base\H.ValueConverter\H.ValueConverter.csproj" />
    <ProjectReference Include="..\..\Modules\H.Modules.Identity\H.Modules.Identity.csproj" />
    <ProjectReference Include="..\..\Modules\H.Modules.Login\H.Modules.Login.csproj" />
    <ProjectReference Include="..\..\Modules\H.Modules.Messages.Dialog\H.Modules.Messages.Dialog.csproj" />
    <ProjectReference Include="..\..\Modules\H.Modules.Messages.Form\H.Modules.Messages.Form.csproj" />
    <ProjectReference Include="..\..\Modules\H.Modules.Messages.Snack\H.Modules.Messages.Snack.csproj" />
    <ProjectReference Include="..\..\Modules\H.Modules.Operation\H.Modules.Operation.csproj" />
    <ProjectReference Include="..\..\Modules\H.Modules.Project\H.Modules.Project.csproj" />
    <ProjectReference Include="..\..\Modules\H.Modules.Theme\H.Modules.Theme.csproj" />
    <ProjectReference Include="..\..\Themes\H.Theme\H.Theme.csproj" />
    <ProjectReference Include="..\..\Windows\H.Windows.Dialog\H.Windows.Dialog.csproj" />
    <ProjectReference Include="..\..\Windows\H.Windows.Main\H.Windows.Main.csproj" />

    <ProjectReference Include="..\..\DataBases\H.DataBases.Sqlite\H.DataBases.Sqlite.csproj" />
    <ProjectReference Include="..\..\Modules\H.Modules.Messages.Dialog\H.Modules.Messages.Dialog.csproj" />
    <ProjectReference Include="..\..\Modules\H.Modules.Setting\H.Modules.Setting.csproj" />
    <ProjectReference Include="..\..\Modules\H.Modules.SplashScreen\H.Modules.SplashScreen.csproj" />
    <ProjectReference Include="..\..\Themes\H.Theme\H.Theme.csproj" />

    <ProjectReference Include="..\..\Presenters\H.Presenters.Repository\H.Presenters.Repository.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.16">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="8.0.16" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.1" />
    <PackageReference Include="Microsoft.Xaml.Behaviors.Wpf" Version="1.1.77" />
    <PackageReference Include="VideoLAN.LibVLC.Windows" Version="3.0.21" />
  </ItemGroup>
</Project>
