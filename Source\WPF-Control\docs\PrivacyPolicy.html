<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WPF-Control 隐私政策</title>
    <style>
        :root {
            --primary-color: #2b579a; /* WPF主题色 */
            --text-color: #333;
            --bg-color: #f9f9f9;
        }
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background: var(--bg-color);
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 1px solid #eee;
            padding-bottom: 20px;
        }
        h1 {
            color: var(--primary-color);
        }
        h2 {
            margin-top: 25px;
            color: var(--primary-color);
            border-left: 4px solid var(--primary-color);
            padding-left: 10px;
        }
        .last-updated {
            font-style: italic;
            color: #666;
        }
        footer {
            margin-top: 50px;
            text-align: center;
            font-size: 0.9em;
            color: #666;
        }
        a {
            color: var(--primary-color);
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
        @media (max-width: 600px) {
            body {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <header>
        <h1>WPF-Control 隐私政策</h1>
        <div class="last-updated">最后更新日期：2025年4月8日</div>
    </header>

    <section>
        <h2>1. 简介</h2>
        <p>HeBianGu/WPF-Control（以下简称"本项目"）是一个开源的WPF UI控件库，托管于GitHub平台。本隐私政策说明我们如何处理与项目相关的数据。</p>
    </section>

    <section>
        <h2>2. 收集的信息</h2>
        <h3>2.1 不主动收集的数据</h3>
        <p>本项目作为代码库，<strong>不会主动收集、存储或处理您的个人数据</strong>（如姓名、邮箱、IP地址等）。</p>
        
        <h3>2.2 GitHub的自动数据</h3>
        <p>若您通过GitHub访问本项目，GitHub可能会根据其隐私政策收集以下数据（我们无法控制）：</p>
        <ul>
            <li>您的GitHub用户名、仓库访问记录</li>
            <li>IP地址、设备信息</li>
            <li>Cookies（参考<a href="https://docs.github.com/en/site-policy/privacy-policies/github-privacy-statement" target="_blank">GitHub隐私政策</a>）</li>
        </ul>
    </section>

    <section>
        <h2>3. 数据使用方式</h2>
        <ul>
            <li><strong>代码下载与使用</strong>：本项目代码可自由下载、修改，无需提供个人信息。</li>
            <li><strong>问题反馈</strong>：若您通过GitHub Issues提交问题，您的GitHub用户名和评论内容将公开可见。</li>
        </ul>
    </section>

    <section>
        <h2>4. 第三方服务</h2>
        <p>本项目可能依赖以下第三方服务，其隐私政策独立于本项目：</p>
        <ul>
            <li><strong>GitHub</strong>：代码托管平台（<a href="https://docs.github.com/en/site-policy/privacy-policies/github-privacy-statement" target="_blank">隐私政策</a>）</li>
            <li><strong>NuGet</strong>：若通过NuGet分发，需遵守<a href="https://www.nuget.org/policies/Privacy" target="_blank">NuGet条款</a></li>
        </ul>
    </section>

    <section>
        <h2>5. Cookies与跟踪技术</h2>
        <p>本项目<strong>不直接使用Cookies或跟踪技术</strong>，但GitHub页面可能使用必要的功能Cookie。</p>
    </section>

    <section>
        <h2>6. 数据安全</h2>
        <ul>
            <li>本项目代码公开透明，无后端服务器存储数据。</li>
            <li>请自行保护您在GitHub等平台上的账户安全。</li>
        </ul>
    </section>

    <section>
        <h2>7. 隐私政策更新</h2>
        <p>我们可能修订本政策，更新后会发布在GitHub仓库中。</p>
    </section>

    <section>
        <h2>8. 联系我们</h2>
        <p>如有隐私相关问题，请通过<a href="https://github.com/HeBianGu/WPF-Control/issues" target="_blank">GitHub Issues</a>提交。</p>
    </section>

    <footer>
        <p>© 2025 WPF-Control 项目组 | <a href="https://github.com/HeBianGu/WPF-Control" target="_blank">项目主页</a></p>
    </footer>
</body>
</html>