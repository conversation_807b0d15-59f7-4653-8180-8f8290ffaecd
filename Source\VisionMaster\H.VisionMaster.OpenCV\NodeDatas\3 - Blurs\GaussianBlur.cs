﻿// Copyright (c) HeBianGu Authors. All Rights Reserved. 
// Author: HeBianGu 
// Github: https://github.com/HeBianGu/WPF-Control 
// Document: https://hebiangu.github.io/WPF-Control-Docs  
// QQ:908293466 Group:971261058 
// bilibili: https://space.bilibili.com/370266611 
// Licensed under the MIT License (the "License")

using H.VisionMaster.NodeGroup.Groups.Blurs;

namespace H.VisionMaster.OpenCV.NodeDatas.Filter;
[Icon(FontIcons.InPrivate)]
[Display(Name = "高斯滤波", GroupName = "对图像进行卷积，能够有效去除噪声并保留图像的整体结构", Order = 1)]
public class GaussianBlur : OpenCVNodeDataBase, IBlurGroupableNodeData
{
    public override void LoadDefault()
    {
        base.LoadDefault();
        this.KSize = new System.Windows.Size(7, 7);
    }
    private System.Windows.Size _ksize = new System.Windows.Size(7, 7);
    /// <summary>
    /// 高斯核的大小，通常是一个奇数（如 3x3、5x5 等
    /// </summary>
    [Display(Name = "核大小", GroupName = VisionPropertyGroupNames.RunParameters, Description = "核的大小决定了滤波的范围。核越大，平滑效果越强，但计算量也会增加")]
    public System.Windows.Size KSize
    {
        get { return _ksize; }
        set
        {
            _ksize = value;
            RaisePropertyChanged();
            this.UpdateInvokeCurrent();
        }
    }

    private double _sigmaX;
    [DefaultValue(0.0)]
    [Display(Name = "X方向标准差", GroupName = VisionPropertyGroupNames.RunParameters, Description = "SigmaX 越大，高斯核在水平方向越平坦，平滑效果越强。SigmaX 越小，高斯核在水平方向越尖锐，平滑效果越弱。")]
    public double SigmaX
    {
        get { return _sigmaX; }
        set
        {
            _sigmaX = value;
            RaisePropertyChanged();
            this.UpdateInvokeCurrent();
        }
    }

    private double _sigmaY;
    [DefaultValue(0.0)]
    [Display(Name = "Y方向标准差", GroupName = VisionPropertyGroupNames.RunParameters, Description = "SigmaY 越大，高斯核在垂直方向越平坦，平滑效果越强。SigmaY 越小，高斯核在垂直方向越尖锐，平滑效果越弱。")]
    public double SigmaY
    {
        get { return _sigmaY; }
        set
        {
            _sigmaY = value;
            RaisePropertyChanged();
            this.UpdateInvokeCurrent();
        }
    }

    private BorderTypes _borderType = BorderTypes.Default;
    [DefaultValue(BorderTypes.Default)]
    [Display(Name = "边界处理", GroupName = VisionPropertyGroupNames.RunParameters, Description = "由于滤波核在边界无法完全覆盖图像，需要指定如何填充边界外的像素")]
    public BorderTypes BorderType
    {
        get { return _borderType; }
        set
        {
            _borderType = value;
            RaisePropertyChanged();
            this.UpdateInvokeCurrent();
        }
    }

    protected override FlowableResult<Mat> Invoke(ISrcVisionNodeData<Mat> srcImageNodeData, IVisionNodeData<Mat> from, IFlowableDiagramData diagram)
    {
        Mat preMat = from.Mat;
        Cv2.GaussianBlur(preMat, preMat, KSize.ToCVSize(), SigmaX, SigmaY, BorderType);
        return this.OK(preMat.Clone());
    }
}
