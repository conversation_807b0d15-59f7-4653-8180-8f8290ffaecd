﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:p="clr-namespace:H.Components.Modbus.Presenters">
    <DataTemplate DataType="{x:Type p:MasterState}">
        <DockPanel x:Name="dp">
            <FontIconTextBlock x:Name="tb"
                               Margin="5,0" />
            <TextBlock x:Name="tb_name" />
        </DockPanel>
        <DataTemplate.Triggers>
            <DataTrigger Binding="{Binding}"
                         Value="Waitting">
                <Setter TargetName="tb" Property="Text" Value="&#xED5A;" />
                <Setter TargetName="tb_name" Property="Text" Value="等待" />
            </DataTrigger>
            <DataTrigger Binding="{Binding}"
                         Value="Connected">
                <Setter TargetName="tb" Property="Text" Value="&#xE839;" />
                <Setter TargetName="dp" Property="TextBlock.Foreground" Value="{DynamicResource {x:Static BrushKeys.Green}}" />
                <Setter TargetName="tb_name" Property="Text" Value="已连接" />
            </DataTrigger>
            <DataTrigger Binding="{Binding}"
                         Value="Unconnet">
                <Setter TargetName="tb" Property="Text" Value="&#xE8CD;" />
                <Setter TargetName="dp" Property="TextBlock.Foreground" Value="{DynamicResource {x:Static BrushKeys.Red}}" />
                <Setter TargetName="tb_name" Property="Text" Value="未连接" />

            </DataTrigger>
            <DataTrigger Binding="{Binding}"
                         Value="ReadError">
                <Setter TargetName="tb" Property="Text" Value="&#xEB56;" />
                <Setter TargetName="dp" Property="TextBlock.Foreground" Value="{DynamicResource {x:Static BrushKeys.Orange}}" />
                <Setter TargetName="tb_name" Property="Text" Value="读取错误" />

            </DataTrigger>
            <DataTrigger Binding="{Binding}"
                         Value="Connectting">
                <Setter TargetName="tb" Property="Text" Value="&#xE14B;" />
                <Setter TargetName="dp" Property="TextBlock.Foreground" Value="{DynamicResource {x:Static BrushKeys.Accent}}" />
                <Setter TargetName="tb_name" Property="Text" Value="正在连接..." />

            </DataTrigger>
            <DataTrigger Binding="{Binding}"
                         Value="Stopped">
                <Setter TargetName="tb" Property="Text" Value="&#xEB55;" />
                <Setter TargetName="dp" Property="TextBlock.Foreground" Value="{DynamicResource {x:Static BrushKeys.ForegroundAssist}}" />
                <Setter TargetName="tb_name" Property="Text" Value="已停止" />
            </DataTrigger>
        </DataTemplate.Triggers>
    </DataTemplate>
</ResourceDictionary>