﻿<ApplicationBase x:Class="H.App.FileManager.App"
                 xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                 xmlns:b="http://schemas.microsoft.com/xaml/behaviors"
                 xmlns:h="https://github.com/HeBianGu"
                 xmlns:local="clr-namespace:H.App.FileManager">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!--<FontSizeTheme Type="Default" />
                <LayoutTheme Type="Default" />
                <ColorTheme Type="Dark" />
                <ConciseStyle />-->
                <ResourceDictionary Source="pack://application:,,,/H.App.FileManager;component/Presenter/FileView.xaml" />
                <ResourceDictionary Source="pack://application:,,,/H.App.FileManager;component/Presenter/ImageView.xaml" />
                <ResourceDictionary Source="pack://application:,,,/H.App.FileManager;component/Presenter/VideoView.xaml" />
                <ResourceDictionary Source="pack://application:,,,/H.App.FileManager;component/Presenter/TorrentView.xaml" />
                <ResourceDictionary Source="pack://application:,,,/H.App.FileManager;component/Presenter/MoreFileViewBase.xaml" />
                <ResourceDictionary Source="pack://application:,,,/H.App.FileManager;component/Presenter/MoreImageView.xaml" />

                <ResourceDictionary Source="pack://application:,,,/H.App.FileManager;component/Project/FileProjectItem.xaml" />
                <ResourceDictionary Source="pack://application:,,,/H.App.FileManager;component/ViewModel/FileRepositoryBindable.xaml" />
            </ResourceDictionary.MergedDictionaries>
            <!--<DataTemplate DataType="{x:Type local:fm_dd_video_image}">
                <DockPanel>
                    <TextBlock HorizontalAlignment="Center"
                               DockPanel.Dock="Bottom"
                               TextTrimming="CharacterEllipsis"
                               ToolTip="{Binding Url}">
                        <Run Text="{Binding TimeStamp}" />
                        <Run Text="{Binding Name}" />
                    </TextBlock>
                    <Image Source="{Binding ., Converter={local:GetFileToViewImageConverter}, ConverterParameter=100, IsAsync=True}" />
                </DockPanel>
            </DataTemplate>-->
        </ResourceDictionary>
    </Application.Resources>
</ApplicationBase>
