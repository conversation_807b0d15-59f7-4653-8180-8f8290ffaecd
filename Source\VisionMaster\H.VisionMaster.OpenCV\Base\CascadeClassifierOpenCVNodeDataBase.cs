﻿// Copyright (c) HeBianGu Authors. All Rights Reserved. 
// Author: HeBianGu 
// Github: https://github.com/HeBianGu/WPF-Control 
// Document: https://hebiangu.github.io/WPF-Control-Docs  
// QQ:908293466 Group:971261058 
// bilibili: https://space.bilibili.com/370266611 
// Licensed under the MIT License (the "License")

using H.VisionMaster.NodeGroup.Groups.Others;
using Point = OpenCvSharp.Point;
using Rect = OpenCvSharp.Rect;
using Size = OpenCvSharp.Size;

namespace H.VisionMaster.OpenCV.Base;

[Icon(FontIcons.EmojiTabPeople)]
public abstract class CascadeClassifierOpenCVNodeDataBase : OpenCVNodeDataBase, IOtherGroupableNodeData
{
    public override void LoadDefault()
    {
        base.LoadDefault();
        this.MinSize = new System.Windows.Size(30, 30);
        this.MaxSize = new System.Windows.Size(500, 500);
    }
    private double _scaleFactor = 1.1;
    [DefaultValue(1.1)]
    [Display(Name = "缩放比例", GroupName = VisionPropertyGroupNames.RunParameters, Description = "图像缩放比例(1.01-1.5)，用于多尺度检测")]
    public double ScaleFactor
    {
        get { return _scaleFactor; }
        set
        {
            _scaleFactor = value;
            RaisePropertyChanged();
        }
    }

    private int _minNeighbors = 3;
    [DefaultValue(3)]
    [Display(Name = "邻近数目", GroupName = VisionPropertyGroupNames.RunParameters, Description = "候选矩形保留的邻近数目，值越大检测越严格")]
    public int MinNeighbors
    {
        get { return _minNeighbors; }
        set
        {
            _minNeighbors = value;
            RaisePropertyChanged();
        }
    }

    private HaarDetectionTypes _flags = HaarDetectionTypes.ScaleImage;
    [DefaultValue(HaarDetectionTypes.ScaleImage)]
    [Display(Name = "Flags", GroupName = VisionPropertyGroupNames.RunParameters)]
    public HaarDetectionTypes Flags
    {
        get { return _flags; }
        set
        {
            _flags = value;
            RaisePropertyChanged();
        }
    }

    private System.Windows.Size _minSize = new System.Windows.Size(30, 30);
    [Display(Name = "最小尺寸", GroupName = "目标的最小尺寸")]
    public System.Windows.Size MinSize
    {
        get { return _minSize; }
        set
        {
            _minSize = value;
            RaisePropertyChanged();
        }
    }

    private System.Windows.Size _maxSize = new System.Windows.Size(500, 500);
    [DefaultValue(null)]
    [Display(Name = "最大尺寸", GroupName = "目标的最大尺寸")]
    public System.Windows.Size MaxSize
    {
        get { return _maxSize; }
        set
        {
            _maxSize = value;
            RaisePropertyChanged();
        }
    }

    private int _matchingCountResult;
    [ReadOnly(true)]
    [Display(Name = "匹配数量", GroupName = VisionPropertyGroupNames.ResultParameters, Description = "结果参数，此结果可应用再条件分支等作为判断参数")]
    public int MatchingCountResult
    {
        get { return _matchingCountResult; }
        set
        {
            _matchingCountResult = value;
            RaisePropertyChanged();
        }
    }

    protected Tuple<Mat, Rect[]> DetectFace(CascadeClassifier cascade, Mat src)
    {
        Mat result;
        using (Mat gray = new Mat())
        {
            result = src.Clone();
            Cv2.CvtColor(src, gray, ColorConversionCodes.BGR2GRAY);

            // Detect faces
            Rect[] faces = cascade.DetectMultiScale(
                gray, this.ScaleFactor, this.MinNeighbors, this.Flags, this.MinSize.ToCVSize(), this.MaxSize.ToCVSize());

            // Render all detected faces
            foreach (Rect face in faces)
            {
                Point center = new Point
                {
                    X = (int)(face.X + face.Width * 0.5),
                    Y = (int)(face.Y + face.Height * 0.5)
                };
                Size axes = new Size
                {
                    Width = (int)(face.Width * 0.5),
                    Height = (int)(face.Height * 0.5)
                };
                Cv2.Ellipse(result, center, axes, 0, 0, 360, VisionSettings.Instance.OutputColor.ToScalar(), result.ToThickness());
            }

            this.MatchingCountResult = faces.Count();
            return Tuple.Create(result, faces);
        }
    }
}
