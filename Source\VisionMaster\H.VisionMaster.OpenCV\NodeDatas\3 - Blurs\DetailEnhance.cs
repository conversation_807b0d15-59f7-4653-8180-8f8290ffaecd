﻿// Copyright (c) HeBianGu Authors. All Rights Reserved. 
// Author: HeBianGu 
// Github: https://github.com/HeBianGu/WPF-Control 
// Document: https://hebiangu.github.io/WPF-Control-Docs  
// QQ:908293466 Group:971261058 
// bilibili: https://space.bilibili.com/370266611 
// Licensed under the MIT License (the "License")

using H.VisionMaster.NodeGroup.Groups.Blurs;

namespace H.VisionMaster.OpenCV.NodeDatas.Filter;
[Icon(FontIcons.InPrivate)]
[Display(Name = "细节增强", GroupName = "细节增强是一种图像处理技术，旨在突出图像中的细节部分（如纹理、边缘、微小特征等），使图像看起来更加清晰和丰富", Order = 20)]
public class DetailEnhance : OpenCVNodeDataBase, IBlurGroupableNodeData
{
    private float _sigmaS = 10f;
    [PropertyItem(typeof(FloatSliderTextPropertyItem))]
    [DefaultValue(10f)]
    [Display(Name = "空间标准差", GroupName = VisionPropertyGroupNames.RunParameters, Description = "较大的 SigmaS 会使滤波核覆盖更广的区域，平滑效果更明显；较小的 SigmaS 则限制滤波核的作用范围，保留更多细节")]
    [Range(0.0f, 200.0f)]
    public float SigmaS
    {
        get { return _sigmaS; }
        set
        {
            _sigmaS = value;
            RaisePropertyChanged();
            this.UpdateInvokeCurrent();
        }
    }

    private float _sigmaR = 0.15f;
    [PropertyItem(typeof(FloatSliderTextPropertyItem))]
    [DefaultValue(0.15f)]
    [Display(Name = "范围标准差", GroupName = VisionPropertyGroupNames.RunParameters, Description = "较大的 SigmaR 允许像素值差异较大的像素参与平滑，平滑效果更强；较小的 SigmaR 则更注重保留边缘，避免平滑边缘区域")]
    [Range(0.0f, 1.0f)]
    public float SigmaR
    {
        get { return _sigmaR; }
        set
        {
            _sigmaR = value;
            RaisePropertyChanged();
            this.UpdateInvokeCurrent();
        }
    }

    protected override FlowableResult<Mat> Invoke(ISrcVisionNodeData<Mat> srcImageNodeData, IVisionNodeData<Mat> from, IFlowableDiagramData diagram)
    {
        Mat src = from.Mat;
        Mat detailEnhance = new Mat();
        Cv2.DetailEnhance(src, detailEnhance, this.SigmaS, this.SigmaR);
        return this.OK(detailEnhance);
    }
}
