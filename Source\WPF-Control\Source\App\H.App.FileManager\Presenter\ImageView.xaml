﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:b="http://schemas.microsoft.com/xaml/behaviors"
                    xmlns:h="https://github.com/HeBianGu"
                    xmlns:local="clr-namespace:H.App.FileManager">
    <DataTemplate DataType="{x:Type local:fm_dd_image}">
        <Grid>
            <DockPanel>
                <Border BorderBrush="{DynamicResource {x:Static h:BrushKeys.BorderBrush}}"
                        BorderThickness="1">
                    <Grid Margin="5">
                        <Image Width="100"
                               Source="{Binding Url, Converter={x:Static h:Converter.GetFileImageSourceInMemory}, ConverterParameter=100, IsAsync=True}" />
                        <Image Width="30"
                               Height="30"
                               HorizontalAlignment="Right"
                               VerticalAlignment="Bottom"
                               Source="{Binding Url, Converter={h:GetFilePathToSystemInfoIconConverter}, IsAsync=True, ConverterParameter=100}" />
                    </Grid>
                </Border>
                <ItemsControl MinWidth="50"
                              Margin="5"
                              ToolTip="{Binding Url}">
                    <TextBlock MaxHeight="50"
                               FontWeight="Bold"
                               Text="{Binding Name}"
                               TextTrimming="CharacterEllipsis"
                               TextWrapping="Wrap"
                               ToolTip="{Binding Name}" />
                    <TextBlock Foreground="{DynamicResource {x:Static h:BrushKeys.Orange}}"
                               Text="{Binding FavoritePath}"
                               ToolTip="{Binding RelativeSource={RelativeSource Mode=Self}, Path=Text}" />
                    <TextBlock Foreground="{DynamicResource {x:Static h:BrushKeys.Green}}"
                               Text="{Binding Object}"
                               ToolTip="{Binding RelativeSource={RelativeSource Mode=Self}, Path=Text}" />
                    <TextBlock Foreground="{DynamicResource {x:Static h:BrushKeys.Orange}}"
                               Text="{Binding Tags}"
                               ToolTip="{Binding RelativeSource={RelativeSource Mode=Self}, Path=Text}" />
                    <TextBlock Foreground="{DynamicResource {x:Static h:BrushKeys.Pink}}"
                               Text="{Binding Area}"
                               ToolTip="{Binding RelativeSource={RelativeSource Mode=Self}, Path=Text}" />
                    <TextBlock Foreground="{DynamicResource {x:Static h:BrushKeys.LightBlue}}"
                               Text="{Binding Articulation}"
                               ToolTip="{Binding RelativeSource={RelativeSource Mode=Self}, Path=Text}" />
                </ItemsControl>
            </DockPanel>
            <Border HorizontalAlignment="Left"
                    VerticalAlignment="Top"
                    Background="{DynamicResource {x:Static h:BrushKeys.Red}}"
                    CornerRadius="2"
                    Visibility="{Binding Watched, Converter={x:Static h:Converter.GetTrueToCollapsed}}">
                <TextBlock Margin="5,3"
                           Foreground="White"
                           Text="New" />
            </Border>

            <TextBlock HorizontalAlignment="Left"
                       VerticalAlignment="Bottom"
                       FontWeight="Bold"
                       Foreground="{DynamicResource {x:Static h:BrushKeys.Orange}}">
                <Run Text="{Binding Score}" /><Run Text="分" />
            </TextBlock>

            <ToggleButton HorizontalAlignment="Right"
                          VerticalAlignment="Bottom"
                          BorderThickness="0"
                          Content="M655.58253 161.724518c-54.76554 0-104.459127 20.519015-145.894532 59.815819-41.652156-39.730304-90.543766-59.815819-145.865632-59.815819-132.116447 0-239.60285 115.079884-239.60285 256.531038 0 82.834686 36.948677 140.454103 64.042447 182.705934 63.421096 99.755648 194.670543 203.253849 267.267396 256.068638l3.366852 2.456502c14.450011 11.234883 31.862273 17.188288 50.372737 17.188287 17.433938 0 34.45605-5.657179 49.231186-16.335737l0.946476-0.700825c0.599675-0.455175 1.784576-1.322176 3.496902-2.564877 72.575178-52.923164 203.867974-156.551415 267.881522-256.299838 26.970945-42.071206 63.919622-99.683398 63.919621-182.525309-0.007225-141.443929-107.291329-256.523813-239.162125-256.523813z"
                          IsChecked="{Binding Favorite}">
                <ToggleButton.Style>
                    <Style BasedOn="{StaticResource {x:Static h:ToggleButtonKeys.Geometry}}"
                           TargetType="ToggleButton">
                        <Style.Triggers>
                            <Trigger Property="IsChecked" Value="True">
                                <Setter Property="Foreground" Value="{DynamicResource {x:Static h:BrushKeys.Red}}" />
                            </Trigger>
                        </Style.Triggers>
                    </Style>
                </ToggleButton.Style>
            </ToggleButton>
        </Grid>
    </DataTemplate>


    <DataTemplate DataType="{x:Type local:ImageView}">
        <Grid>
            <h:Zoombox x:Name="zoomview"
                       Background="Transparent"
                       DragModifiers=""
                       Focusable="True"
                       IsTabStop="True"
                       NavigateOnPreview="False"
                       RelativeZoomModifiers=""
                       ViewStackIndex="0"
                       ZoomOn="Content">
                <h:Zoombox.ViewStack>
                    <!--<h:ZoomboxView>Empty</h:ZoomboxView>-->
                    <h:ZoomboxView>Fit</h:ZoomboxView>
                    <!--<h:ZoomboxView>Fill</h:ZoomboxView>
                    <h:ZoomboxView>Center</h:ZoomboxView>
                    <h:ZoomboxView>10 10 100 100</h:ZoomboxView>
                    <h:ZoomboxView>2.0</h:ZoomboxView>-->
                </h:Zoombox.ViewStack>
                <b:Interaction.Triggers>
                    <h:MouseTrigger ClickCount="2"
                                    Mode="Right"
                                    MouseButton="Left"
                                    UseHandle="False">
                        <!--<b:InvokeCommandAction Command="{Binding Source={x:Static h:IocProject.Instance}, Path=Current.File.NextCommand}" />-->
                        <h:CallMethodActionEx MethodName="FitToBounds"
                                              TargetObject="{Binding ElementName=zoomview}" />
                    </h:MouseTrigger>
                    <h:MouseTrigger ClickCount="2"
                                    Mode="Left"
                                    MouseButton="Left"
                                    UseHandle="False">
                        <!--<b:InvokeCommandAction Command="{Binding Source={x:Static h:IocProject.Instance}, Path=Current.File.PreviousCommand}" />-->
                        <h:CallMethodActionEx MethodName="FitToBounds"
                                              TargetObject="{Binding ElementName=zoomview}" />
                    </h:MouseTrigger>
                </b:Interaction.Triggers>
                <Image Source="{Binding Model.Url, NotifyOnSourceUpdated=True, NotifyOnTargetUpdated=True}">
                    <b:Interaction.Triggers>
                        <b:EventTrigger EventName="SourceUpdated">
                            <h:CallMethodActionEx MethodName="FitToBounds"
                                                  TargetObject="{Binding ElementName=zoomview}" />
                        </b:EventTrigger>
                        <b:EventTrigger EventName="TargetUpdated">
                            <h:CallMethodActionEx MethodName="FitToBounds"
                                                  TargetObject="{Binding ElementName=zoomview}" />
                        </b:EventTrigger>
                    </b:Interaction.Triggers>
                </Image>
            </h:Zoombox>
            <DockPanel HorizontalAlignment="Right"
                       VerticalAlignment="Top"
                       DockPanel.Dock="Top"
                       LastChildFill="False">
                <Button Command="{x:Static h:Zoombox.Fit}"
                        CommandTarget="{Binding ElementName=zoomview}"
                        Content="适配"
                        DockPanel.Dock="Right" />

                <Button Command="{x:Static h:Zoombox.Fill}"
                        CommandTarget="{Binding ElementName=zoomview}"
                        Content="填充"
                        DockPanel.Dock="Right" />

                <Button Command="{Binding Source={x:Static h:IocProject.Instance}, Path=Current.File.PreviousCommand}"
                        Content="上一个"
                        DockPanel.Dock="Right" />

                <Button Command="{Binding Source={x:Static h:IocProject.Instance}, Path=Current.File.NextCommand}"
                        Content="下一个"
                        DockPanel.Dock="Right" />

            </DockPanel>
        </Grid>
    </DataTemplate>
</ResourceDictionary>