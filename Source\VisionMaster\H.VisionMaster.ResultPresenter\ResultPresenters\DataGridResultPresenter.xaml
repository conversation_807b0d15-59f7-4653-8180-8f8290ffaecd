﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:b="http://schemas.microsoft.com/xaml/behaviors"
                    xmlns:lr="clr-namespace:H.VisionMaster.ResultPresenter.ResultPresenters">
    <DataTemplate DataType="{x:Type lr:DataGridResultPresenterBase}">
        <DataGrid AutoGenerateColumns="False"
                  IsReadOnly="True"
                  ItemsSource="{Binding Collection}"
                  SelectedItem="{Binding SelectedItem}">
            <b:Interaction.Triggers>
                <b:EventTrigger EventName="SelectionChanged">
                    <b:InvokeCommandAction Command="{Binding SelectionChangedCommand}"
                                           CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=DataGrid}, Path=SelectedItem}" />
                </b:EventTrigger>
            </b:Interaction.Triggers>
            <b:Interaction.Behaviors>
                <DataGridAutoColumnBehavior DataGridLength="*"
                                            Type="{Binding Type}" />
            </b:Interaction.Behaviors>
        </DataGrid>
    </DataTemplate>

</ResourceDictionary>