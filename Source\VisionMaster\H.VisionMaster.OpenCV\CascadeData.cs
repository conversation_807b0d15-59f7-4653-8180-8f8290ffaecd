﻿// Copyright (c) HeBianGu Authors. All Rights Reserved. 
// Author: HeBianGu 
// Github: https://github.com/HeBianGu/WPF-Control 
// Document: https://hebiangu.github.io/WPF-Control-Docs  
// QQ:908293466 Group:971261058 
// bilibili: https://space.bilibili.com/370266611 
// Licensed under the MIT License (the "License")

namespace H.VisionMaster.OpenCV;

/// <summary>
/// Text file paths
/// </summary>
public static class CascadeData
{
    public const string Eye = "Data/Cascade/haarcascades/haarcascade_eye.xml";
    public const string Eyeglasses = "Data/Cascade/haarcascades/haarcascade_eye_tree_eyeglasses.xml";
    public const string Frontalcatface = "Data/Cascade/haarcascades/haarcascade_frontalcatface.xml";
    public const string Frontalface = "Data/Cascade/haarcascades/haarcascade_frontalface_default.xml";
    public const string Fullbody = "Data/Cascade/haarcascades/haarcascade_fullbody.xml";
    public const string Lefteye = "Data/Cascade/haarcascades/haarcascade_lefteye_2splits.xml";
    public const string Licence_plate = "Data/Cascade/haarcascades/haarcascade_licence_plate_rus_16stages.xml";
    public const string Lowerbody = "Data/Cascade/haarcascades/haarcascade_lowerbody.xml";
    public const string Profileface = "Data/Cascade/haarcascades/haarcascade_profileface.xml";
    public const string Righteye = "Data/Cascade/haarcascades/haarcascade_righteye_2splits.xml";
    public const string Russian_plate_number = "Data/Cascade/haarcascades/haarcascade_russian_plate_number.xml";
    public const string Smile = "Data/Cascade/haarcascades/haarcascade_smile.xml";
    public const string Upperbody = "Data/Cascade/haarcascades/haarcascade_upperbody.xml";
}
