﻿// Copyright (c) HeBianGu Authors. All Rights Reserved. 
// Author: HeBianGu 
// Github: https://github.com/HeBianGu/WPF-Control 
// Document: https://hebiangu.github.io/WPF-Control-Docs  
// QQ:908293466 Group:971261058 
// bilibili: https://space.bilibili.com/370266611 
// Licensed under the MIT License (the "License")

using H.VisionMaster.NodeGroup.Groups.Blurs;

namespace H.VisionMaster.OpenCV.NodeDatas.Filter;
[Icon(FontIcons.InPrivate)]
[Display(Name = "素描", GroupName = "素描效果通常强调图像的边缘和轮廓，同时减少或去除颜色和纹理信息", Order = 3)]
public class PencilSketch : OpenCVNodeDataBase, IBlurGroupableNodeData
{
    private float _sigmaS = 60f;
    [PropertyItem(typeof(FloatSliderTextPropertyItem))]
    [DefaultValue(60f)]
    [Display(Name = "空间标准差", GroupName = VisionPropertyGroupNames.RunParameters, Description = "较大的 SigmaS 会使滤波核覆盖更广的区域，平滑效果更明显；较小的 SigmaS 则限制滤波核的作用范围，保留更多细节")]
    [Range(0F, 200F)]
    public float SigmaS
    {
        get { return _sigmaS; }
        set
        {
            _sigmaS = value;
            RaisePropertyChanged();
            this.UpdateInvokeCurrent();
        }
    }

    private float _sigmaR = 0.07f;
    [PropertyItem(typeof(FloatSliderTextPropertyItem))]
    [DefaultValue(0.07f)]
    [Display(Name = "范围标准差", GroupName = VisionPropertyGroupNames.RunParameters, Description = "较大的 SigmaR 允许像素值差异较大的像素参与平滑，平滑效果更强；较小的 SigmaR 则更注重保留边缘，避免平滑边缘区域")]
    [Range(0F, 1.0F)]
    public float SigmaR
    {
        get { return _sigmaR; }
        set
        {
            _sigmaR = value;
            RaisePropertyChanged();
            this.UpdateInvokeCurrent();
        }
    }

    private float _shadeFactor = 0.01f;
    [PropertyItem(typeof(FloatSliderTextPropertyItem))]
    [DefaultValue(0.01f)]
    [Display(Name = "SigmaR", GroupName = VisionPropertyGroupNames.RunParameters)]
    [Range(0F, 0.01F)]
    public float ShadeFactor
    {
        get { return _shadeFactor; }
        set
        {
            _shadeFactor = value;
            RaisePropertyChanged();
            this.UpdateInvokeCurrent();
        }
    }

    private PencilOutType _pencilOutType;
    [DefaultValue(PencilOutType.Src)]
    [Display(Name = "OutType", GroupName = VisionPropertyGroupNames.RunParameters)]
    public PencilOutType PencilOutType
    {
        get { return _pencilOutType; }
        set
        {
            _pencilOutType = value;
            RaisePropertyChanged();
            this.UpdateInvokeCurrent();
        }
    }

    protected override FlowableResult<Mat> Invoke(ISrcVisionNodeData<Mat> srcImageNodeData, IVisionNodeData<Mat> from, IFlowableDiagramData diagram)
    {
        Mat src = from.Mat;
        //  Do ：输出8位的单通道图像
        Mat pencil1 = new Mat();
        //  Do ：输出与源图像相同大小与类型的图像
        Mat pencil2 = new Mat();
        Cv2.PencilSketch(src, pencil1, pencil2, this.SigmaS, this.SigmaR, this.ShadeFactor);
        return this.OK(this.PencilOutType == PencilOutType.Src ? pencil2 : pencil1);
    }
}

public enum PencilOutType
{
    Src, Channel8
}
