﻿// Copyright (c) HeBianGu Authors. All Rights Reserved. 
// Author: HeBianGu 
// Github: https://github.com/HeBianGu/WPF-Control 
// Document: https://hebiangu.github.io/WPF-Control-Docs  
// QQ:908293466 Group:971261058 
// bilibili: https://space.bilibili.com/370266611 
// Licensed under the MIT License (the "License")

namespace H.VisionMaster.Zoo.Images;

/// <summary>
/// Image file Paths
/// </summary>
public static class ImagePath
{
    public const string Lenna = "Assets/lenna.png";
    public const string Lenna511 = "Assets/lenna511.png";
    public const string Girl = "Assets/Girl.bmp";
    public const string Mandrill = "Assets/Mandrill.bmp";
    public const string Goryokaku = "Assets/goryokaku.jpg";
    public const string Maltese = "Assets/maltese.jpg";
    public const string Cake = "Assets/cake.bmp";
    public const string Fruits = "Assets/fruits.jpg";
    public const string Penguin1 = "Assets/penguin1.png";
    public const string Penguin1b = "Assets/penguin1b.png";
    public const string Penguin2 = "Assets/penguin2.png";
    public const string Distortion = "Assets/Calibration/01.jpg";
    public const string Calibration = "Assets/Calibration/00.jpg";
    public const string SurfBox = "Assets/box.png";
    public const string SurfBoxinscene = "Assets/box_in_scene.png";
    public const string TsukubaLeft = "Assets/tsukuba_left.png";
    public const string TsukubaRight = "Assets/tsukuba_right.png";
    public const string Square1 = "Assets/Squares/pic1.png";
    public const string Square2 = "Assets/Squares/pic2.png";
    public const string Square3 = "Assets/Squares/pic3.png";
    public const string Square4 = "Assets/Squares/pic4.png";
    public const string Square5 = "Assets/Squares/pic5.png";
    public const string Square6 = "Assets/Squares/pic6.png";
    public const string Shapes = "Assets/shapes.png";
    public const string Yalta = "Assets/yalta.jpg";
    public const string Depth16Bit = "Assets/16bit.png";
    public const string Hand = "Assets/hand_p.jpg";
    public const string Asahiyama = "Assets/asahiyama.jpg";
    public const string Balloon = "Assets/Balloon.png";
    public const string Newspaper = "Assets/very_old_newspaper.png";
    public const string Binarization = "Assets/binarization_sample.bmp";
    public const string Walkman = "Assets/walkman.jpg";
    public const string Cat = "Assets/cat.jpg";
    public const string Match1 = "Assets/match1.png";
    public const string Match2 = "Assets/match2.png";
    public const string Aruco = "Assets/aruco_markers_photo.jpg";

    public const string Cvmorph = "Assets/cvmorph.png";

    public const string Circle = "Assets/circle.png";
    public const string Pentagon = "Assets/pentagon.png";
}
