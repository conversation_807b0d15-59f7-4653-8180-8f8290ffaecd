﻿// Copyright (c) HeBianGu Authors. All Rights Reserved. 
// Author: HeBianGu 
// Github: https://github.com/HeBianGu/WPF-Control 
// Document: https://hebiangu.github.io/WPF-Control-Docs  
// QQ:908293466 Group:971261058 
// bilibili: https://space.bilibili.com/370266611 
// Licensed under the MIT License (the "License")

namespace H.VisionMaster.OpenCV;

/// <summary>
/// Text file paths
/// </summary>
public static class TextPath
{
    public const string Camera = "Data/Text/camera.xml";
    public const string HaarCascade = "Data/Text/haarcascade_frontalface_default.xml";
    public const string HaarCascadeAlt = "Data/Text/haarcascade_frontalface_alt.xml";
    public const string LatentSvmCat = "Data/Text/cat.xml";
    public const string Mushroom = "Data/Text/agaricus-lepiota.data";
    public const string LetterRecog = "Data/Text/letter-recognition.data";
    public const string LbpCascade = "Data/Text/lbpcascade_frontalface.xml";
}
