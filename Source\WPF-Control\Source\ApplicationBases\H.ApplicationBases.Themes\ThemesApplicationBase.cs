﻿// Copyright (c) HeBianGu Authors. All Rights Reserved. 
// Author: HeBianGu 
// Github: https://github.com/HeBianGu/WPF-Control 
// Document: https://hebiangu.github.io/WPF-Control-Docs  
// QQ:908293466 Group:971261058 
// bilibili: https://space.bilibili.com/370266611 
// Licensed under the MIT License (the "License")

using H.Extensions.ApplicationBase;
using Microsoft.Extensions.DependencyInjection;

namespace H.ApplicationBases.Themes
{
    public abstract partial class ThemesApplicationBase : ApplicationBase
    {
        protected override void ConfigureServices(IServiceCollection services)
        {
            base.ConfigureServices(services);
            services.AddDefaultThemeServices();
        }
        protected override void Configure(IApplicationBuilder app)
        {
            base.Configure(app);
            app.UseDefaultThemeOptions();
        }
    }
}
