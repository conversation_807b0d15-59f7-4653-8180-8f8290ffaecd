﻿// Copyright (c) HeBianGu Authors. All Rights Reserved. 
// Author: HeBianGu 
// Github: https://github.com/HeBianGu/WPF-Control 
// Document: https://hebiangu.github.io/WPF-Control-Docs  
// QQ:908293466 Group:971261058 
// bilibili: https://space.bilibili.com/370266611 
// Licensed under the MIT License (the "License")

using H.VisionMaster.NodeGroup.Groups.Morphologys;

namespace H.VisionMaster.OpenCV.NodeDatas.Morphology;
[Icon(FontIcons.HomeGroup)]
[Display(Name = "开运算", GroupName = "形态学", Description = "腐蚀 + 膨胀，先腐蚀后膨胀，用于去除小物体或噪声", Order = 21)]
public class Open : MorphologyOpenCVNodeDataBase, IMorphologyGroupableNodeData
{
    protected override MorphTypes GetMorphType()
    {
        return MorphTypes.Open;
    }
}
