﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:b="http://schemas.microsoft.com/xaml/behaviors"
                    xmlns:h="https://github.com/HeBianGu"
                    xmlns:local="clr-namespace:H.App.FileManager">
    <DataTemplate DataType="{x:Type local:FileRepositoryBindable}">
        <DockPanel>
            <DockPanel DockPanel.Dock="Top"
                       LastChildFill="False">
                <DockPanel VerticalAlignment="Center"
                           Background="{DynamicResource {x:Static h:BrushKeys.MouseOver}}">
                    <Button Command="{Binding RefreshCommand}"
                            Content="加载文件" />
                    <ToggleButton Content="{x:Static h:Geometrys.Down}"
                                  Style="{DynamicResource {x:Static h:ToggleButtonKeys.Geometry}}">
                        <b:Interaction.Behaviors>
                            <h:MouseOverContextMenuBehavior />
                        </b:Interaction.Behaviors>
                        <Control.ContextMenu>
                            <ContextMenu ItemsSource="{Binding UpdateCommands}"
                                         StaysOpen="True">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <MenuItem Command="{Binding .}"
                                                  Header="{Binding Name}" />
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ContextMenu>
                        </Control.ContextMenu>
                    </ToggleButton>
                </DockPanel>

                <DockPanel Margin="1,0"
                           VerticalAlignment="Center"
                           Background="{DynamicResource {x:Static h:BrushKeys.MouseOver}}">
                    <Button Command="{Binding RefreshCommand}"
                            Content="更多操作" />
                    <ToggleButton Cattach.CornerRadius="2"
                                  Content="{x:Static h:Geometrys.Down}"
                                  Style="{DynamicResource {x:Static h:ToggleButtonKeys.Geometry}}">
                        <b:Interaction.Behaviors>
                            <h:MouseOverContextMenuBehavior />
                        </b:Interaction.Behaviors>
                        <Control.ContextMenu>
                            <ContextMenu ItemsSource="{Binding MoreCommands}">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <MenuItem Command="{Binding .}"
                                                  Header="{Binding Name}" />
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ContextMenu>
                        </Control.ContextMenu>
                    </ToggleButton>
                </DockPanel>

                <h:PropertyFilterBox Margin="1,0"
                                     DockPanel.Dock="Right"
                                     Type="{x:Type local:fm_dd_file}">
                    <b:Interaction.Triggers>
                        <b:EventTrigger EventName="FilterChanged">
                            <b:ChangePropertyAction PropertyName="Filter1"
                                                    TargetObject="{Binding Collection}"
                                                    Value="{Binding RelativeSource={RelativeSource AncestorType=h:PropertyFilterBox}, Path=Filter}" />
                        </b:EventTrigger>
                        <b:EventTrigger EventName="Loaded">
                            <b:ChangePropertyAction PropertyName="Filter1"
                                                    TargetObject="{Binding Collection}"
                                                    Value="{Binding RelativeSource={RelativeSource AncestorType=h:PropertyFilterBox}, Path=Filter}" />
                        </b:EventTrigger>
                    </b:Interaction.Triggers>
                </h:PropertyFilterBox>

                <h:PropertyOrderBox Margin="1,0"
                                    DockPanel.Dock="Right"
                                    Type="{x:Type local:fm_dd_file}">
                    <b:Interaction.Triggers>
                        <b:EventTrigger EventName="OrderChanged">
                            <b:ChangePropertyAction PropertyName="Order1"
                                                    TargetObject="{Binding Collection}"
                                                    Value="{Binding RelativeSource={RelativeSource AncestorType=h:PropertyOrderBox}, Path=Order}" />
                        </b:EventTrigger>
                        <b:EventTrigger EventName="Loaded">
                            <b:ChangePropertyAction PropertyName="Order1"
                                                    TargetObject="{Binding Collection}"
                                                    Value="{Binding RelativeSource={RelativeSource AncestorType=h:PropertyOrderBox}, Path=Order}" />
                        </b:EventTrigger>
                    </b:Interaction.Triggers>
                </h:PropertyOrderBox>

                <h:TextFilterBox x:Name="tfb"
                                 Width="200"
                                 Margin="1,0"
                                 DockPanel.Dock="Right">
                    <b:Interaction.Triggers>
                        <b:EventTrigger EventName="FilterChanged">
                            <b:ChangePropertyAction PropertyName="Filter2"
                                                    TargetObject="{Binding Collection}"
                                                    Value="{Binding ElementName=tfb, Path=Filter}" />
                        </b:EventTrigger>
                    </b:Interaction.Triggers>
                </h:TextFilterBox>
            </DockPanel>

            <!--<ContentPresenter Content="{Ioc Type={x:Type IProjectViewPresenter}}" DockPanel.Dock="Bottom" />-->

            <DockPanel Margin="0,2,0,4"
                       DockPanel.Dock="Bottom">
                <TextBlock VerticalAlignment="Center">
                    <Run Text=" 合计：" />
                    <Run Text="{Binding Collection.FilterSource.Count, Mode=OneWay}" />
                    <Run Text="/" />
                    <Run Text="{Binding Collection.Cache.Count, Mode=OneWay}" />
                </TextBlock>

                <h:PagerBox HorizontalAlignment="Right"
                            MaxPageCount="{Binding Path=Collection.TotalPage, Mode=TwoWay}"
                            PageIndex="{Binding Path=Collection.PageIndex, Mode=TwoWay}" />
            </DockPanel>

            <Border Margin="0,1"
                    BorderBrush="{DynamicResource {x:Static BrushKeys.BorderBrush}}"
                    BorderThickness="1">
                <Control>
                    <b:Interaction.Triggers>
                        <b:EventTrigger EventName="MouseDoubleClick">
                            <b:InvokeCommandAction Command="{Binding MouseDoubleClickCommand}"
                                                   CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=ListBox}, Path=SelectedItem.Model}" />
                        </b:EventTrigger>
                    </b:Interaction.Triggers>
                    <Control.Template>
                        <ControlTemplate>
                            <Grid Background="Transparent">
                                <Grid.ContextMenu>
                                    <ContextMenu IsOpen="True"
                                                 ItemsSource="{Binding Collection.SelectedItem.Model, Converter={local:GetFileMenuItemConverter}}"
                                                 StaysOpen="True">
                                        <ItemsControl.ItemTemplate>
                                            <HierarchicalDataTemplate ItemsSource="{Binding Nodes}">
                                                <TextBlock VerticalAlignment="Center"
                                                           Text="{Binding}" />
                                            </HierarchicalDataTemplate>
                                        </ItemsControl.ItemTemplate>
                                        <ContextMenu.ItemContainerStyle>
                                            <Style BasedOn="{StaticResource {x:Static h:MenuItemKeys.Default}}"
                                                   TargetType="MenuItem">
                                                <Setter Property="Command" Value="{Binding Model}" />
                                                <Setter Property="Header" Value="{Binding Model.Name}" />
                                                <Setter Property="IsCheckable" Value="{Binding IsCheckable, Mode=TwoWay}" />
                                                <Setter Property="IsChecked" Value="{Binding IsChecked, Mode=TwoWay}" />
                                            </Style>
                                        </ContextMenu.ItemContainerStyle>
                                    </ContextMenu>
                                </Grid.ContextMenu>
                                <DataGrid x:Name="dg"
                                          ItemsSource="{Binding Collection.Source, IsAsync=True}"
                                          SelectedItem="{Binding Collection.SelectedItem}"
                                          Visibility="Collapsed">
                                    <b:Interaction.Behaviors>
                                        <h:DataGridAutoColumnBehavior BindingPath="Model.{0}"
                                                                      DataGridLength="*"
                                                                      Type="{x:Type local:fm_dd_file}">
                                            <h:DataGridAutoColumnBehavior.HomeColumns>
                                                <DataGridCheckBoxColumn Header="全选" />
                                            </h:DataGridAutoColumnBehavior.HomeColumns>
                                        </h:DataGridAutoColumnBehavior>

                                        <!--<h:ItemsControlFilterBehavior Filter="{Binding ElementName=fb, Path=Filter}"
                                                                      Filter1="{Binding ElementName=tfb, Path=Filter}"
                                                                      Filter2="{Binding ElementName=sfb, Path=Filter}"
                                                                      Filter3="{Binding ElementName=sfb1, Path=Filter}"
                                                                      Filter4="{Binding ElementName=sfb2, Path=Filter}"
                                                                      ItemsSource="{Binding Students}" />-->

                                    </b:Interaction.Behaviors>
                                </DataGrid>

                                <ListBox x:Name="lb"
                                         h:Cattach.ItemHeight="Auto"
                                         h:Cattach.ItemPadding="0"
                                         ItemsSource="{Binding Collection.Source, IsAsync=True}"
                                         SelectedItem="{Binding Collection.SelectedItem}"
                                         Visibility="Collapsed">
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <Border Padding="2"
                                                    BorderBrush="{DynamicResource {x:Static h:BrushKeys.BorderBrush}}"
                                                    BorderThickness="1"
                                                    CornerRadius="2">

                                                <DockPanel x:Name="dp"
                                                           Width="200"
                                                           Height="200">
                                                    <TextBlock Margin="2,5"
                                                               DockPanel.Dock="Bottom"
                                                               Text="{Binding Model.Name}"
                                                               TextTrimming="CharacterEllipsis"
                                                               ToolTip="{Binding Model.Url}" />
                                                    <Image Source="{Binding Model, Converter={local:GetFileToViewImageSourceConverter}}"
                                                           Stretch="{Binding Source={x:Static local:AppSetting.Instance}, Path=Stretch}" />
                                                </DockPanel>
                                            </Border>
                                            <DataTemplate.Triggers>
                                                <DataTrigger Binding="{Binding Source={x:Static local:AppSetting.Instance}, Path=ViewSizeMode}"
                                                             Value="Auto">
                                                    <Setter TargetName="dp" Property="Width" Value="Auto" />
                                                    <Setter TargetName="dp" Property="Height" Value="Auto" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Source={x:Static local:AppSetting.Instance}, Path=ViewSizeMode}"
                                                             Value="ExtraLarge">
                                                    <Setter TargetName="dp" Property="Width" Value="600" />
                                                    <Setter TargetName="dp" Property="Height" Value="600" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Source={x:Static local:AppSetting.Instance}, Path=ViewSizeMode}"
                                                             Value="Large">
                                                    <Setter TargetName="dp" Property="Width" Value="400" />
                                                    <Setter TargetName="dp" Property="Height" Value="400" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Source={x:Static local:AppSetting.Instance}, Path=ViewSizeMode}"
                                                             Value="Small">
                                                    <Setter TargetName="dp" Property="Width" Value="100" />
                                                    <Setter TargetName="dp" Property="Height" Value="100" />
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Source={x:Static local:AppSetting.Instance}, Path=ViewSizeMode}"
                                                             Value="ExtraSmall">
                                                    <Setter TargetName="dp" Property="Width" Value="50" />
                                                    <Setter TargetName="dp" Property="Height" Value="50" />
                                                </DataTrigger>
                                            </DataTemplate.Triggers>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                    <ItemsControl.ItemsPanel>
                                        <ItemsPanelTemplate>
                                            <WrapPanel HorizontalAlignment="Center" />
                                        </ItemsPanelTemplate>
                                    </ItemsControl.ItemsPanel>
                                    <ListBox.ItemContainerStyle>
                                        <Style BasedOn="{StaticResource {x:Static h:ListBoxItemKeys.Default}}"
                                               TargetType="ListBoxItem">
                                            <Setter Property="Padding" Value="2" />
                                        </Style>
                                    </ListBox.ItemContainerStyle>
                                </ListBox>

                                <h:GridSplitterBox x:Name="lb_view"
                                                   MenuMaxWidth="300"
                                                   MenuMinWidth="120"
                                                   Mode="Hidden"
                                                   Visibility="Collapsed">
                                    <h:GridSplitterBox.MenuContent>
                                        <ListBox h:Cattach.ItemHeight="Auto"
                                                 Cattach.ItemBorderBrush="{DynamicResource {x:Static BrushKeys.BorderBrush}}"
                                                 DockPanel.Dock="Left"
                                                 ItemsSource="{Binding Collection.Source, IsAsync=True}"
                                                 ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                                                 ScrollViewer.VerticalScrollBarVisibility="Auto"
                                                 SelectedItem="{Binding Collection.SelectedItem}">
                                            <b:Interaction.Triggers>
                                                <b:EventTrigger EventName="SelectionChanged">
                                                    <b:InvokeCommandAction Command="{Binding SelectionChangedCommand}"
                                                                           CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=ListBox}, Path=SelectedItem.Model}" />
                                                </b:EventTrigger>
                                                <b:EventTrigger EventName="MouseDoubleClick">
                                                    <b:InvokeCommandAction Command="{Binding MouseDoubleClickCommand}"
                                                                           CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=ListBox}, Path=SelectedItem.Model}" />
                                                </b:EventTrigger>
                                            </b:Interaction.Triggers>
                                            <ListBox.ItemContainerStyle>
                                                <Style BasedOn="{StaticResource {x:Static h:ListBoxItemKeys.Default}}"
                                                       TargetType="ListBoxItem">
                                                    <Setter Property="BorderThickness" Value="1" />
                                                    <Setter Property="Margin" Value="1" />
                                                    <Setter Property="Padding" Value="5" />
                                                </Style>
                                            </ListBox.ItemContainerStyle>
                                            <ItemsControl.ItemTemplate>
                                                <DataTemplate>
                                                    <DockPanel>
                                                        <ContentPresenter Content="{Binding Model}" />
                                                    </DockPanel>
                                                </DataTemplate>
                                            </ItemsControl.ItemTemplate>
                                            <ItemsControl.ItemsPanel>
                                                <ItemsPanelTemplate>
                                                    <VirtualizingStackPanel Orientation="Vertical" />
                                                </ItemsPanelTemplate>
                                            </ItemsControl.ItemsPanel>
                                        </ListBox>
                                    </h:GridSplitterBox.MenuContent>
                                    <Grid Background="Transparent">
                                        <ContentPresenter Content="{Binding Collection.SelectedItem.Model, Converter={local:GetFileToViewConverter}}" />
                                    </Grid>
                                </h:GridSplitterBox>
                            </Grid>
                            <ControlTemplate.Triggers>
                                <DataTrigger Binding="{Binding Source={x:Static local:AppSetting.Instance}, Path=DisplayMode}"
                                             Value="DataGrid">
                                    <Setter TargetName="dg" Property="Visibility" Value="Visible" />
                                </DataTrigger>
                                <DataTrigger Binding="{Binding Source={x:Static local:AppSetting.Instance}, Path=DisplayMode}"
                                             Value="ListBox">
                                    <Setter TargetName="lb" Property="Visibility" Value="Visible" />
                                </DataTrigger>
                                <DataTrigger Binding="{Binding Source={x:Static local:AppSetting.Instance}, Path=DisplayMode}"
                                             Value="View">
                                    <Setter TargetName="lb_view" Property="Visibility" Value="Visible" />
                                </DataTrigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Control.Template>
                </Control>
            </Border>
        </DockPanel>
    </DataTemplate>
</ResourceDictionary>