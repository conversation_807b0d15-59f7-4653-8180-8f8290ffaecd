﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:b="http://schemas.microsoft.com/xaml/behaviors"
                    xmlns:h="https://github.com/HeBianGu"
                    xmlns:local="clr-namespace:H.App.FileManager">

    <DataTemplate DataType="{x:Type local:MoreFileViewBase}">
        <h:GridSplitterBox
                           MenuMaxWidth="300"
                           MenuMinWidth="50"
                           MenuWidth="100"
                           Mode="Hidden"
                           Style="{DynamicResource {x:Static h:GridSplitterBox.BottomKey}}">
            <h:GridSplitterBox.MenuContent>
                <ListBox Cattach.ItemBorderBrush="{DynamicResource {x:Static BrushKeys.BorderBrush}}"
                         Cattach.ItemHeight="Auto"
                         ItemsSource="{Binding More}"
                         ScrollViewer.HorizontalScrollBarVisibility="Auto"
                         ScrollViewer.VerticalScrollBarVisibility="Disabled"
                         SelectedItem="{Binding SelectedItem}"
                         Visibility="{Binding ElementName=tb_list, Path=IsChecked, Converter={x:Static h:Converter.GetTrueToVisible}}">
                    <b:Interaction.Triggers>
                        <b:EventTrigger EventName="SelectionChanged">
                            <b:InvokeCommandAction Command="{Binding SelectionChangedCommand}"
                                                   CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=ListBox}, Path=SelectedItem.Model}" />
                        </b:EventTrigger>
                        <b:EventTrigger EventName="MouseDoubleClick">
                            <b:InvokeCommandAction Command="{Binding MouseDoubleClickCommand}"
                                                   CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=ListBox}, Path=SelectedItem.Model}" />
                        </b:EventTrigger>
                    </b:Interaction.Triggers>
                    <ListBox.ItemContainerStyle>
                        <Style BasedOn="{StaticResource {x:Static h:ListBoxItemKeys.Default}}"
                               TargetType="ListBoxItem">
                            <Setter Property="BorderThickness" Value="1" />
                            <Setter Property="Margin" Value="1" />
                            <Setter Property="Padding" Value="5" />
                        </Style>
                    </ListBox.ItemContainerStyle>
                    <ItemsControl.ItemsPanel>
                        <ItemsPanelTemplate>
                            <VirtualizingStackPanel Orientation="Horizontal" />
                        </ItemsPanelTemplate>
                    </ItemsControl.ItemsPanel>
                    <ItemsControl.ItemTemplate>
                        <DataTemplate>
                            <Grid MaxWidth="200">
                                <ContentPresenter Content="{Binding Model}" />
                                <TextBlock HorizontalAlignment="Center"
                                           VerticalAlignment="Center"
                                           FontSize="20"
                                           FontWeight="Bold"
                                           Text="{Binding Description}" />
                            </Grid>
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                    <ListBox.Template>
                        <ControlTemplate TargetType="ListBox">
                            <Border Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    CornerRadius="2">
                                <ScrollViewer Padding="{TemplateBinding Padding}"
                                              Focusable="false">
                                    <b:Interaction.Behaviors>
                                        <h:ScrollViewerBebavior UseHorizontalMouseWheel="True"
                                                                UseMouseWheelHijack="True" />
                                    </b:Interaction.Behaviors>
                                    <ItemsPresenter SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                                </ScrollViewer>
                            </Border>
                        </ControlTemplate>
                    </ListBox.Template>
                </ListBox>
            </h:GridSplitterBox.MenuContent>
            <h:GridSplitterBox.ToggleStyle>
                <Style BasedOn="{StaticResource {x:Static h:GridSplitterBox.ToggleKey}}"
                       TargetType="ToggleButton">
                    <Setter Property="Grid.Row" Value="0" />
                </Style>
            </h:GridSplitterBox.ToggleStyle>
            <DockPanel>
                <TextBlock Margin="5"
                           HorizontalAlignment="Left"
                           VerticalAlignment="Top"
                           DockPanel.Dock="Top"
                           Text="{Binding Model.Url}" />
                <Grid Background="Transparent"
                      Focusable="True">
                    <Grid.InputBindings>
                        <KeyBinding Key="Right"
                                    Command="{Binding NextCommand}" />
                        <KeyBinding Key="Backspace"
                                    Command="{Binding NextCommand}" />
                        <KeyBinding Key="Return"
                                    Command="{Binding NextCommand}" />
                        <KeyBinding Key="Down"
                                    Command="{Binding NextCommand}" />
                        <KeyBinding Key="Left"
                                    Command="{Binding PreviousCommand}" />
                        <KeyBinding Key="Up"
                                    Command="{Binding PreviousCommand}" />
                    </Grid.InputBindings>
                    <ContentPresenter Content="{Binding SelectedItem}" />
                    <Button HorizontalAlignment="Left"
                            VerticalAlignment="Center"
                            Command="{Binding PreviousCommand}"
                            Content="上一个" />

                    <Button HorizontalAlignment="Right"
                            VerticalAlignment="Center"
                            Command="{Binding NextCommand}"
                            Content="下一个" />
                </Grid>
            </DockPanel>
        </h:GridSplitterBox>
    </DataTemplate>
</ResourceDictionary>