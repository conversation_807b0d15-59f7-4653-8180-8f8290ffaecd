﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <!-- Import the common properties to support NuGet restore -->
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <!-- A target framework version is required by Visual Studio.  It can be any version with a targeting pack installed. -->
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <!-- The configuration and platform will be used to determine which assemblies to include from solution and
				 project documentation sources -->
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{0baa0ed5-48cc-493e-86b0-a47dae180398}</ProjectGuid>
    <SHFBSchemaVersion>2017.9.26.0</SHFBSchemaVersion>
    <!-- AssemblyName, Name, and RootNamespace are not used by SHFB but Visual Studio adds them anyway -->
    <AssemblyName>Documentation</AssemblyName>
    <RootNamespace>Documentation</RootNamespace>
    <Name>Documentation</Name>
    <!-- SHFB properties -->
    <FrameworkVersion>.NET Framework 4.7.2</FrameworkVersion>
    <OutputPath>.\Help\</OutputPath>
    <HtmlHelpName>Documentation</HtmlHelpName>
    <Language>zh-CN</Language>
    <DocumentationSources>
      <DocumentationSource sourceFile="..\Source\Services\H.Services.Common\bin\Debug\net7.0-windows\H.Services.Common.dll" xmlns="" />
    </DocumentationSources>
    <HelpFileFormat>Website</HelpFileFormat>
    <SyntaxFilters>C#</SyntaxFilters>
    <PresentationStyle>Default2022</PresentationStyle>
    <CleanIntermediates>True</CleanIntermediates>
    <KeepLogFile>True</KeepLogFile>
    <DisableCodeBlockComponent>False</DisableCodeBlockComponent>
    <IndentHtml>False</IndentHtml>
    <BuildAssemblerVerbosity>OnlyWarningsAndErrors</BuildAssemblerVerbosity>
    <SaveComponentCacheCapacity>100</SaveComponentCacheCapacity>
    <HelpTitle>WPF-Control</HelpTitle>
    <HelpFileVersion>1.0.0.0</HelpFileVersion>
    <NamingMethod>Guid</NamingMethod>
    <ContentPlacement>AboveNamespaces</ContentPlacement>
    <RootNamespaceContainer>False</RootNamespaceContainer>
    <NamespaceGrouping>False</NamespaceGrouping>
    <MaximumGroupParts>2</MaximumGroupParts>
    <Preliminary>False</Preliminary>
    <SdkLinkTarget>Blank</SdkLinkTarget>
  </PropertyGroup>
  <!-- There are no properties for these groups.  AnyCPU needs to appear in order for Visual Studio to perform
			 the build.  The others are optional common platform types that may appear. -->
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|x86' ">
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|x86' ">
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|x64' ">
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|x64' ">
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|Win32' ">
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|Win32' ">
  </PropertyGroup>
  <!-- Import the common build targets during NuGet restore because before the packages are being installed, $(SHFBROOT) is not set yet -->
  <Import Project="$(MSBuildToolsPath)\Microsoft.Common.targets" Condition="'$(MSBuildRestoreSessionId)' != ''" />
  <!-- Import the SHFB build targets during build -->
  <Import Project="$(SHFBROOT)\SandcastleHelpFileBuilder.targets" Condition="'$(MSBuildRestoreSessionId)' == ''" />
  <!-- The pre-build and post-build event properties must appear *after* the targets file import in order to be
			 evaluated correctly. -->
  <PropertyGroup>
    <PreBuildEvent>
    </PreBuildEvent>
    <PostBuildEvent>
    </PostBuildEvent>
    <RunPostBuildEvent>OnBuildSuccess</RunPostBuildEvent>
  </PropertyGroup>
</Project>