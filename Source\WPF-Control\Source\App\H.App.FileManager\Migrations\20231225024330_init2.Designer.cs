﻿// <auto-generated />
using System;
using H.App.FileManager;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace H.App.FileManager.Migrations
{
    [DbContext(typeof(DataContext))]
    [Migration("20231225024330_init2")]
    partial class init2
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "7.0.8")
                .HasAnnotation("Proxies:ChangeTracking", false)
                .HasAnnotation("Proxies:CheckEquality", false)
                .HasAnnotation("Proxies:LazyLoading", true);

            modelBuilder.Entity("H.App.FileManager.fm_dd_file", b =>
                {
                    b.Property<string>("ID")
                        .HasColumnType("TEXT")
                        .HasColumnName("id")
                        .HasColumnOrder(0);

                    b.Property<DateTime>("CDATE")
                        .HasColumnType("TEXT");

                    b.Property<string>("Discriminator")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("Extend")
                        .HasColumnType("TEXT");

                    b.Property<bool>("Favorite")
                        .HasColumnType("INTEGER");

                    b.Property<int>("ISENBLED")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("PlayCount")
                        .HasColumnType("TEXT");

                    b.Property<string>("Project")
                        .HasColumnType("TEXT");

                    b.Property<string>("Score")
                        .HasColumnType("TEXT");

                    b.Property<long>("Size")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Tags")
                        .HasColumnType("TEXT");

                    b.Property<string>("Type")
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("UDATE")
                        .HasColumnType("TEXT");

                    b.Property<string>("Url")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("ID");

                    b.ToTable("fm_dd_files");

                    b.HasDiscriminator<string>("Discriminator").HasValue("fm_dd_file");

                    b.UseTphMappingStrategy();
                });

            modelBuilder.Entity("H.App.FileManager.fm_dd_audio", b =>
                {
                    b.HasBaseType("H.App.FileManager.fm_dd_file");

                    b.HasDiscriminator().HasValue("fm_dd_audio");
                });

            modelBuilder.Entity("H.App.FileManager.fm_dd_image", b =>
                {
                    b.HasBaseType("H.App.FileManager.fm_dd_file");

                    b.Property<int>("PixelHeight")
                        .HasColumnType("INTEGER");

                    b.Property<int>("PixelWidth")
                        .HasColumnType("INTEGER");

                    b.HasDiscriminator().HasValue("fm_dd_image");
                });

            modelBuilder.Entity("H.App.FileManager.fm_dd_video", b =>
                {
                    b.HasBaseType("H.App.FileManager.fm_dd_image");

                    b.HasDiscriminator().HasValue("fm_dd_video");
                });
#pragma warning restore 612, 618
        }
    }
}
