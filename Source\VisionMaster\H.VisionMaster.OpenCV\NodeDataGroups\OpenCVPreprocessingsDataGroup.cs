// Copyright (c) HeBianGu Authors. All Rights Reserved.
// Author: HeBianGu
// Github: https://github.com/HeBianGu/WPF-Control
// Document: https://hebiangu.github.io/WPF-Control-Docs
// QQ:908293466 Group:971261058
// bilibili: https://space.bilibili.com/370266611
// Licensed under the MIT License (the "License")

global using H.Extensions.Common;
using H.Controls.Diagram.Datas;
using H.Iocable;
using H.VisionMaster.NodeGroup.Groups.Preprocessings;
using H.VisionMaster.PluginInterface.Services;
using System;
using System.Linq;

namespace H.VisionMaster.OpenCV.NodeDataGroups;

public class OpenCVPreprocessingsDataGroup : PreprocessingDataGroup, IImageDataGroup
{
    protected override IEnumerable<INodeData> CreateNodeDatas()
    {
        // 获取原有的静态插件
        var staticPlugins = this.GetType().Assembly.GetInstances<IPreprocessingGroupableNodeData>();

        // 获取动态加载的插件
        var dynamicPlugins = GetDynamicPlugins();

        // 合并并排序
        return staticPlugins.Concat(dynamicPlugins.Cast<INodeData>()).OrderBy(x => ((IDisplayBindable)x).Order);
    }

    private IEnumerable<IPreprocessingGroupableNodeData> GetDynamicPlugins()
    {
        try
        {
            var pluginLoader = Ioc.GetService<IPluginLoaderService>(false);
            if (pluginLoader != null)
            {
                return pluginLoader.GetLoadedPlugins<IPreprocessingGroupableNodeData>();
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"获取动态插件失败: {ex.Message}");
        }

        return Enumerable.Empty<IPreprocessingGroupableNodeData>();
    }
}
